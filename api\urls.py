from django.urls import path
from api import views

urlpatterns = [
    # 管理员登录
    path('loginAdmin', views.administrator_login, name='admin_login'),
    # 商品分类API路由
    path('get_category_list', views.get_category_list, name='get_category_list'),
    path('add_category', views.add_category, name='add_category'),
    path('update_category', views.update_category, name='update_category'),
    path('delete_category', views.delete_category, name='delete_category'),
    path('upload_category_image', views.upload_category_image, name='upload_category_image'),  # 上传分类图片
    # 会员等级API路由
    path('AddMemberLevel', views.add_member_level, name='add_member_level'),
    path('DelMemberLevel', views.delete_member_level, name='delete_member_level'),
    path('GetMemberLevelList', views.get_member_level_list, name='get_member_level_list'),

    # 定价模板API路由
    path('AddPriceTemplate', views.add_price_template, name='add_price_template'),
    path('DelPriceTemplate', views.delete_price_template, name='delete_price_template'),
    path('GetPriceTemplateList', views.get_price_template_list, name='get_price_template_list'),
    path('UpdatePriceTemplate', views.update_price_template, name='update_price_template'),

    # 卡库相关API
    path('AddCardLibrary', views.add_card_library, name='add_card_library'),
    path('DelCardLibrary', views.delete_card_library, name='delete_card_library'),
    path('UpdateCardLibrary', views.update_card_library, name='update_card_library'),
    path('GetCardLibraryList', views.get_card_library_list, name='get_card_library_list'),
    path('GetCardLibraryCardList', views.get_card_library_card_list, name='get_card_library_card_list'),
    path('AddCardLibraryCard', views.add_card_library_card, name='add_card_library_card'),
    path('DelCardLibraryCard', views.delete_card_library_card, name='delete_card_library_card'),
    
    # 商品相关API
    path('GetGoodsList', views.get_goods_list, name='get_goods_list'),
    path('GetGoodsDetail', views.get_goods_detail, name='get_goods_detail'),
    path('AddGoods', views.add_goods, name='add_goods'),
    path('deleteGood', views.delete_goods, name='delete_goods'),
    path('UpdateGoods', views.update_goods, name='update_goods'),
    path('upload_product_image', views.upload_product_image, name='upload_product_image'),  # 上传商品图片
    path('BatchAddDockingProduct', views.BatchAddDockingProduct, name='BatchAddDockingProduct'), # 批量添加对接商品
    
    # 对接中心相关API
    path('addDocking', views.add_docking, name='add_docking'),
    path('getDockingList', views.get_docking_list, name='get_docking_list'),
    path('deleteDocking', views.delete_docking, name='delete_docking'),
    path('updateDocking', views.update_docking, name='update_docking'),

    # 支付渠道相关API
    path('add_payment_method', views.add_payment_method, name='add_payment_method'),
    path('get_payment_method_list', views.get_payment_method_list, name='get_payment_method_list'),
    path('delete_payment_method', views.delete_payment_method, name='delete_payment_method'),
    path('update_payment_method', views.update_payment_method, name='update_payment_method'),

    # 支付回调相关API
    path('epay_notify', views.epay_notify, name='epay_notify'),
    path('alipay_notify', views.alipay_notify, name='alipay_notify'),

    # 聚比价商品信息推送回调API
    path('jbjProductPushCallback', views.jubijia_product_push_callback, name='jubijia_product_push_callback'),

    # 聚比价订单状态异步回调API
    path('jbjOrderCallback', views.jubijia_order_callback, name='jubijia_order_callback'),

    # 本地图片服务API
    path('get_local_image_product', views.get_local_image_product, name='get_local_image_product'),

    # 卡券管理相关API
    path('coupons/list/', views.coupon_list, name='coupon_list'),
    path('coupons/generate/', views.coupon_generate, name='coupon_generate'),
    path('coupons/detail/', views.coupon_detail, name='coupon_detail'),
    path('coupons/delete/', views.coupon_delete, name='coupon_delete'),

    # 订单管理相关API
    path('GetOrdersList', views.get_orders_list, name='get_orders_list'),
    path('orders', views.get_order_detail, name='get_order_detail'),
    path('orders/batch-delete', views.batch_delete_orders, name='batch_delete_orders'),

]