<template>
  <div id="app">
    <!-- 全屏背景效果 -->
    <div class="full-bg" :style="backgroundImageUrl ? { backgroundImage: 'url(' + backgroundImageUrl + ')' } : {}"></div>

    <div class="main-container">
      <div class="col-xs-12 col-sm-10 col-md-8 col-lg-5 center-block" style="float: none;">

      <!-- 弹出公告模态框 -->
      <div class="modal fade" align="left" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showModal">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header-tabs">
              <button type="button" class="close" @click="showModal = false">
                <span aria-hidden="true">×</span>
                <span class="sr-only">Close</span>
              </button>
              <h4 class="modal-title" id="myModalLabel">依思商城</h4>
            </div>
            <div class="modal-body">
              <!-- 公告内容 -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default" @click="showModal = false">知道啦</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台公告模态框 -->
      <div class="modal fade" align="left" id="anounce" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showAnnounceModal" style="display: none;">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header" style="background:linear-gradient(120deg, #31B404 0%, #D7DF01 100%);">
              <button type="button" class="close" @click="showAnnounceModal = false">
                <span aria-hidden="true">×</span>
                <span class="sr-only">Close</span>
              </button>
              <div class="text-center">
                <h4 class="modal-title" id="myModalLabel">
                  <b><span style="color:#fff">依思商城</span></b>
                </h4>
              </div>
            </div>
            <div class="widget flat radius-bordered">
              <div class="widget-header bordered-top bordered-themesecondary">
                <div class="modal-body">
                  <p>
                    <li class="list-group-item">
                      <span class="btn btn-danger btn-xs">1</span> 售后问题可直接联系平台在线QQ客服
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-success btn-xs">2</span> 下单之前请一定要看完该商品的注意事项再进行下单！
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-info btn-xs">3</span> 所有业务全部恢复，都可以正常下单，欢迎尝试
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-warning btn-xs">4</span> 温馨提示：请勿重复下单哦！必须要等待前面任务订单完成才可以下单！
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-primary btn-xs">5</span>
                      <a href="./user/regsite.php">价格贵？不怕，点击0元搭建，在后台超低价下单！</a>
                    </li>
                    <div class="btn-group btn-group-justified">
                      <a target="_blank" class="btn btn-info" href="http://wpa.qq.com/msgrd?v=3&uin=123456&site=qq&menu=yes">
                        <i class="fa fa-qq"></i> 联系客服
                      </a>
                      <a target="_blank" class="btn btn-warning" href="http://qun.qq.com/join.html">
                        <i class="fa fa-users"></i> 官方Q群
                      </a>
                      <a target="_blank" class="btn btn-danger" href="./">
                        <i class="fa fa-cloud-download"></i> APP下载
                      </a>
                    </div>
                  </p>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default" @click="showAnnounceModal = false">我明白了</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Logo区域 -->
      <div class="widget">
        <div class="widget-content themed-background-flat text-center logo-header">
          <div class="logo-avatar-container">
            <div class="logo-avatar">
              <img src="https://q.qlogo.cn/headimg_dl?dst_uin=107766441&spec=640&img_type=jpg" alt="头像" class="avatar-img">
            </div>
          </div>
        </div>

        <div class="text-center logo-title">
          <h2>
            <span class="brand-link">
              <b>依思商城</b>
            </span>
          </h2>
          <p class="brand-subtitle">专业的数字商品交易平台</p>
        </div>

        <!-- Logo下面按钮 -->
        <div class="widget-content text-center action-buttons">
          <div class="btn-group btn-group-justified modern-btn-group">
            <div class="btn-group">
              <a class="btn btn-modern btn-announce" @click="showAnnounceModal = true">
                <i class="fa fa-bullhorn"></i>
                <span>公告</span>
              </a>
            </div>
            <div class="btn-group">
              <a class="btn btn-modern btn-service" @click="showCustomerServiceModal = true">
                <i class="fa fa-qq"></i>
                <span>客服</span>
              </a>
            </div>
            <div class="btn-group">
              <a class="btn btn-modern btn-login" href="user/login.php">
                <i class="fa fa-users"></i>
                <span>登录</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab导航区域 -->
      <div class="block full2 modern-tabs">
        <div class="block-title">
          <ul class="nav nav-tabs modern-nav-tabs" data-toggle="tabs">
            <li class="nav-item" :class="{ active: activeTab === 'shop' }">
              <a href="#shop" @click="activeTab = 'shop'" class="nav-link">
                <i class="fa fa-shopping-cart"></i>
                <span>下单</span>
              </a>
            </li>
            <li class="nav-item" :class="{ active: activeTab === 'search' }">
              <a href="#search" @click="activeTab = 'search'" class="nav-link">
                <i class="fa fa-search"></i>
                <span>查询</span>
              </a>
            </li>
            <li class="nav-item" :class="{ active: activeTab === 'substation' }">
              <a href="#Substation" @click="activeTab = 'substation'" class="nav-link nav-link-special">
                <i class="fa fa-location-arrow fa-spin"></i>
                <span>分站</span>
              </a>
            </li>
            <li class="nav-item" :class="{ active: activeTab === 'more' }">
              <a href="#more" @click="activeTab = 'more'" class="nav-link">
                <i class="fa fa-list"></i>
                <span>更多</span>
              </a>
            </li>
          </ul>
        </div>

        <!-- Tab内容区域 -->
        <div class="tab-content">
          <!-- 在线下单 -->
          <div class="tab-pane" :class="{ active: activeTab === 'shop' }" id="shop">
            <div class="text-center">
              <div
                v-show="showTip"
                :class="['shuaibi-tip', 'animated', tipAnimation, 'text-center', `tip-${tipStyle}`]"
                @click="onTipClick"
                style="cursor: pointer;"
                title="点击切换样式">
                <i class="fa fa-heart text-danger"></i>
                <b>[[ currentTime ]]</b>
              </div>
            </div>

            <div id="goodTypeContents" class="modern-order-form">


              <div class="form-group" id="display_searchBar">
                <div class="input-group">
                  <div class="input-group-addon">搜索商品</div>
                  <input type="text" id="searchkw" class="form-control" placeholder="搜索商品" v-model="searchKeyword" @keydown.enter.prevent="handleSearchClick"/>
                  <div class="input-group-addon search-icon-addon" @click="handleSearchClick">
                    <svg class="order-search-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" shape-rendering="geometricPrecision" image-rendering="optimizeQuality">
                      <path d="M959.266 879.165c0 81.582-81.582 81.582-81.582 81.582l-233.38-233.381c-60.529 43.977-134.777 70.217-215.318 70.217-202.755 0-367.117-164.362-367.117-367.117S226.23 63.349 428.985 63.349s367.117 164.362 367.117 367.117c0 80.541-26.241 154.785-70.217 215.318l233.381 233.381zM428.985 144.931c-157.697 0-285.536 127.838-285.536 285.536s127.838 285.536 285.536 285.536 285.536-127.838 285.536-285.536-127.839-285.536-285.536-285.536z"></path>
                    </svg>
                  </div>
                </div>
              </div>




              <div class="form-group" id="display_price" style="display:none;text-align:center;color:#4169E1;font-weight:bold">
                <div class="input-group">
                  <div class="input-group-addon">商品价格</div>
                  <input type="text" name="need" id="need" class="form-control" style="text-align:center;color:#4169E1;font-weight:bold" disabled v-model="productPrice"/>
                </div>
              </div>

              <div class="form-group" id="display_left" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">库存数量</div>
                  <input type="text" name="leftcount" id="leftcount" class="form-control" disabled v-model="stockCount"/>
                </div>
              </div>

              <div class="form-group" id="display_num" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">下单份数</div>
                  <span class="input-group-btn">
                    <input id="num_min" type="button" class="btn btn-info" style="border-radius: 0px;" value="━" @click="decreaseQuantity">
                  </span>
                  <input id="num" name="num" class="form-control" type="number" min="1" v-model="orderQuantity"/>
                  <span class="input-group-btn">
                    <input id="num_add" type="button" class="btn btn-info" style="border-radius: 0px;" value="✚" @click="increaseQuantity">
                  </span>
                </div>
              </div>

              <div id="inputsname"></div>

              <!-- 一级分类选择 -->
              <div class="form-group" v-show="!isSearchMode">
                <div class="input-group">
                  <div class="input-group-addon">商品分类</div>
                  <select id="category-level-1" class="form-control" v-model="selectedCategoryL1" @change="onCategoryL1Change">
                    <option value="">请选择分类</option>
                    <option v-for="category in categoriesData" :key="category.id" :value="category.id">
                      [[ category.name ]]
                    </option>
                  </select>
                </div>
              </div>

              <!-- 二级分类选择 -->
              <div class="form-group" v-show="showCategoryL2 && !isSearchMode">
                <div class="input-group">
                  <div class="input-group-addon">二级分类</div>
                  <select id="category-level-2" class="form-control" v-model="selectedCategoryL2" @change="onCategoryL2Change">
                    <option value="">请选择二级分类</option>
                    <option v-for="subCategory in currentSubCategories" :key="subCategory.id" :value="subCategory.id">
                      [[ subCategory.name ]]
                    </option>
                  </select>
                </div>
              </div>

              <!-- 商品选择 -->
              <div class="form-group" v-show="showProductSelect">
                <div class="input-group">
                  <div class="input-group-addon">选择商品</div>
                  <select id="product-select" class="form-control" v-model="selectedProduct">
                    <option value="">请选择商品</option>
                    <option v-for="product in productsData" :key="product.id" :value="product.id">
                      [[ product.name ]] - ¥[[ product.price ]]
                    </option>
                  </select>
                </div>
              </div>

              <!-- 商品信息展示区 -->
              <div v-if="productDetails" class="product-details-container">
                <div class="product-info-grid">
                  <div class="info-item">
                    <span class="info-item-label">价格</span>
                    <span class="info-item-value price">¥[[ productDetails.price ]]</span>
                  </div>
                  <div class="info-item">
                    <span class="info-item-label">销量</span>
                    <span class="info-item-value">[[ productDetails.sales_count ]]</span>
                  </div>
                  <div class="info-item">
                    <span class="info-item-label">库存</span>
                    <span class="info-item-value" :class="stockColor">[[ productDetails.stock ]]</span>
                  </div>
                  <div class="info-item" @click="showInfoModal = true" style="cursor: pointer;">
                    <span class="info-item-label">说明</span>
                    <span class="info-item-value info-link">点击查看</span>
                  </div>
                </div>
                <!-- 收货邮箱 -->
                <div class="form-group">
                  <div class="input-group">
                    <div class="input-group-addon">收货邮箱</div>
                    <input type="email" class="form-control" v-model="email" placeholder="用于接收订单信息及查询订单">
                  </div>
                </div>
                <!-- 动态输入框 -->
                <div v-for="(input, index) in dynamicInputs" :key="index" class="form-group">
                  <div class="input-group">
                    <div class="input-group-addon">[[ input.name ]]</div>
                    <input type="text" class="form-control" v-model="inputValues[input.name]" :placeholder="input.tip">
                  </div>
                </div>

                <!-- 优惠券区域 -->
                <div class="form-group coupon-area">
                  <div class="coupon-checkbox-container">
                    <input type="checkbox" v-model="useCoupon" id="use-coupon-checkbox" class="custom-checkbox">
                    <label for="use-coupon-checkbox">使用优惠卷</label>
                  </div>
                  <div v-if="useCoupon" class="coupon-input-container">
                    <input type="text" class="form-control" v-model="couponCode" placeholder="请输入优惠卷码">
                  </div>
                </div>
              </div>

              <!-- 新的立即购买按钮 -->
              <div class="form-group" v-show="selectedProduct">
                <a type="submit" id="submit_buy_new" class="btn btn-block btn-primary" @click="buyNow">立即购买</a>
              </div>


            </div>
          </div>

          <!-- 查询订单 -->
          <div class="tab-pane" :class="{ active: activeTab === 'search' }" id="search">
            <!-- 客服信息卡片 -->
            <div class="customer-service-card">
              <div class="service-avatar">
                <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="客服头像" class="avatar-image">
                <div class="online-status"></div>
              </div>
              <div class="service-info">
                <div class="service-title">
                  <i class="fa fa-user-circle"></i>
                  <span>专属客服</span>
                </div>
                <div class="service-details">
                  <div class="contact-item">
                    <i class="fa fa-qq"></i>
                    <span>123456789</span>
                  </div>
                  <div class="service-desc">售后订单问题请联系客服</div>
                </div>
              </div>
              <div class="service-action">
                <a href="#lxkf" @click="showCustomerServiceModal = true" class="contact-btn">
                  <span>联系客服</span>
                </a>
              </div>
            </div>

            <br>

            <!-- 现代化搜索框 -->
            <div class="search-container">
              <div class="search-input-wrapper">
                <div class="search-icon">
                  <i class="fa fa-search"></i>
                </div>
                <input
                  type="text"
                  v-model="searchQuery"
                  class="modern-search-input"
                  placeholder="请输入订单号查询"
                >
              </div>

              <button class="modern-search-btn" @click="submitQuery">
                <i class="fa fa-search"></i>
                <span>立即查询</span>
              </button>
            </div>

            <br>
            <!-- 查询结果区域 -->
            <div class="query-results" v-show="showQueryResult">
              <!-- 订单卡片列表 -->
              <div class="order-cards" v-if="queryResults.length > 0">
                <div class="order-card" v-for="order in queryResults" :key="order.id">
                  <div class="order-header">
                    <div class="order-id">
                      <i class="fa fa-file-text-o"></i>
                      <span>[[ order.id ]]</span>
                    </div>
                    <div class="order-status" :class="'status-' + order.status">
                      <i class="fa" :class="{
                        'fa-check-circle': order.status === 'completed',
                        'fa-clock-o': order.status === 'processing',
                        'fa-exclamation-triangle': order.status === 'pending',
                        'fa-times-circle': order.status === 'failed'
                      }"></i>
                      <span>[[ order.statusText ]]</span>
                    </div>
                  </div>

                  <div class="order-content">
                    <div class="order-info">
                      <div class="info-item">
                        <label>商品名称</label>
                        <span>[[ order.productName ]]</span>
                      </div>
                      <div class="info-item">
                        <label>下单账号</label>
                        <span>[[ order.account ]]</span>
                      </div>
                      <div class="info-row">
                        <div class="info-item">
                          <label>数量</label>
                          <span>[[ order.quantity ]]</span>
                        </div>
                        <div class="info-item">
                          <label>金额</label>
                          <span class="amount">¥[[ order.amount ]]</span>
                        </div>
                      </div>
                      <div class="info-item">
                        <label>创建时间</label>
                        <span>[[ order.createTime ]]</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div class="empty-state" v-if="queryResults.length === 0">
                <div class="empty-icon">
                  <i class="fa fa-search"></i>
                </div>
                <div class="empty-text">
                  <h4>暂无查询结果</h4>
                  <p>请检查订单号是否正确，或尝试留空查询最新订单</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 开通分站 -->
          <div class="tab-pane animation-fadeInQuick2" :class="{ active: activeTab === 'substation' }" id="Substation">
            <table class="table table-borderless table-pricing">
              <tbody>
                <tr class="active">
                  <td class="btn-effect-ripple" style="overflow: hidden; position: relative;width: 100%; height: 8em;display: block;color: white;margin: auto;background-color: lightskyblue;">
                    <span class="btn-ripple animate" style="height: 546px; width: 546px; top: -212.8px; left: 56.4px;"></span>
                    <h3 style="width:100%;font-size: 1.6em;">
                    </h3>
                    <h3 style="width:100%;font-size: 1.6em;">
                      <i class="fa fa-user-o fa-fw" style="margin-top: 0.7em;"></i><strong>入门级</strong> /
                      <i class="fa fa-user-circle-o fa-fw"></i><strong>旗舰级</strong>
                    </h3>
                    <span style="width: 100%;text-align: center;margin-top: 0.8em;font-size: 1.1em;display: block;">10元 / 20元</span>
                  </td>
                </tr>
                <tr>
                  <td>一模一样的独立网站</td>
                </tr>
                <tr>
                  <td>站长后台和超低秘价</td>
                </tr>
                <tr>
                  <td>余额提成满10元提现</td>
                </tr>
                <tr>
                  <td><strong>旗舰级可以吃下级分站提成</strong></td>
                </tr>
                <tr class="active">
                  <td>
                    <a href="#userjs" @click="showVersionModal = true" class="btn btn-effect-ripple btn-info" style="overflow: hidden; position: relative;">
                      <i class="fa fa-align-justify"></i>
                      <span class="btn-ripple animate" style="height: 100px; width: 100px; top: -24.8px; left: 11.05px;"></span> 版本介绍
                    </a>
                    <a href="user/regsite.php" target="_blank" class="btn btn-effect-ripple btn-danger" style="overflow: hidden; position: relative;">
                      <i class="fa fa-arrow-right"></i> 马上开通
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 更多按钮 -->
          <div class="tab-pane" :class="{ active: activeTab === 'more' }" id="more">
            <div class="row">
              <div class="col-sm-6">
                <a href="./user/" target="_blank" class="widget">
                  <div class="widget-content themed-background-info text-right clearfix" style="color: #fff;">
                    <div class="widget-icon pull-left">
                      <i class="fa fa-certificate"></i>
                    </div>
                    <h2 class="widget-heading h3">
                      <strong>分站后台</strong>
                    </h2>
                    <span>登录分站后台</span>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="panel panel-primary stats-panel">
        <div class="panel-heading stats-header">
          <h3 class="panel-title">
            <i class="fa fa-bar-chart-o"></i>
            <span>数据统计</span>
          </h3>
        </div>
        <div class="stats-grid">
          <div class="stats-item">
            <div class="stats-value-row">
              <div class="stats-number">9</div>
              <div class="stats-unit">天</div>
            </div>
            <div class="stats-label">安全运营</div>
          </div>
          <div class="stats-item">
            <div class="stats-value-row">
              <div class="stats-number">0</div>
              <div class="stats-unit">元</div>
            </div>
            <div class="stats-label">交易总数</div>
          </div>
          <div class="stats-item">
            <div class="stats-value-row">
              <div class="stats-number">0</div>
              <div class="stats-unit">笔</div>
            </div>
            <div class="stats-label">订单总数</div>
          </div>
          <div class="stats-item">
            <div class="stats-value-row">
              <div class="stats-number">0</div>
              <div class="stats-unit">个</div>
            </div>
            <div class="stats-label">代理分站</div>
          </div>
          <div class="stats-item">
            <div class="stats-value-row">
              <div class="stats-number">0</div>
              <div class="stats-unit">元</div>
            </div>
            <div class="stats-label">今日交易</div>
          </div>
          <div class="stats-item">
            <div class="stats-value-row">
              <div class="stats-number">0</div>
              <div class="stats-unit">笔</div>
            </div>
            <div class="stats-label">今日订单</div>
          </div>
        </div>
      </div>

      <!-- 底部导航 -->
      <div class="panel panel-default">
        <div class="text-center">
          <div class="panel-body">
            <span style="font-weight:bold">依思商城 <i class="fa fa-heart text-danger"></i> [[ currentYear ]] | </span>
            <a href="./"><span style="font-weight:bold">[[ currentHost ]]</span></a><br/>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- 商品说明模态框 -->
    <div class="info-modal-overlay" v-show="showInfoModal" @click="showInfoModal = false">
      <div class="info-modal-content" @click.stop>
        <div class="info-modal-header">
          <h4 class="info-modal-title">商品说明</h4>
          <button type="button" class="close" @click="showInfoModal = false">×</button>
        </div>
        <div class="info-modal-body" v-if="productDetails" v-html="productDetails.info">
        </div>
        <div class="info-modal-footer">
          <button type="button" class="btn btn-default" @click="showInfoModal = false">关闭</button>
        </div>
      </div>
    </div>

    <!-- 客服介绍模态框 -->
    <div class="modal fade col-xs-12" align="left" id="lxkf" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showCustomerServiceModal">
      <br><br>
      <div class="modal-dialog panel panel-primary animation-fadeInQuick2">
        <div class="modal-content">
          <div class="list-group-item reed" style="background:linear-gradient(120deg, #5ED1D7 10%, #71D7A2 90%);">
            <button type="button" class="close" @click="showCustomerServiceModal = false">
              <span aria-hidden="true">×</span>
              <span class="sr-only">Close</span>
            </button>
            <div class="text-center">
              <h4 class="modal-title" id="myModalLabel">
                <b><span style="color:#fff">客服与帮助</span></b>
              </h4>
            </div>
          </div>
          <div class="modal-body" id="accordion">
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">为什么订单显示已完成了却一直没到账？</a>
                </h4>
              </div>
              <div id="collapseOne" class="panel-collapse in" style="height: auto;">
                <div class="panel-body">
                  订单显示（已完成）就证明已经提交到服务器内！<br>
                  如果长时间没到账请联系客服处理！<br>
                  订单长时间显示（待处理）请联系客服！
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" class="collapsed">商品什么时候到账？</a>
                </h4>
              </div>
              <div id="collapseTwo" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body">
                  请参考商品简介里面，有关于到账时间的说明。
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" class="collapsed">卡密没有发送我的邮箱？</a>
                </h4>
              </div>
              <div id="collapseThree" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body">
                  没有收到请检查自己邮箱的垃圾箱！也可以去查单区：输入自己下单时填写的邮箱进行查单。<br>
                  查询到订单后点击（详细）就可以看到自己购买的卡密！
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseFourth" class="collapsed">已付款了没有查询到我订单？</a>
                </h4>
              </div>
              <div id="collapseFourth" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body" style="margin-bottom: 6px;">
                  联系客服处理，请提供（付款详细记录截图）（下单商品名称）（下单账号）<br>
                  直接把三个信息发给客服，然后等待客服回复处理（请不要发抖动窗口或者QQ电话）！
                </div>
              </div>
            </div>
            <ul class="list-group" style="margin-bottom: 0px;">
              <li class="list-group-item">
                <div class="media">
                  <span class="pull-left thumb-sm">
                    <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="..." class="img-circle img-thumbnail img-avatar">
                  </span>
                  <div class="pull-right push-15-t">
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=123456789&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-info">联系</a>
                  </div>
                  <div class="pull-left push-10-t">
                    <div class="font-w600 push-5">订单售后客服</div>
                    <div class="text-muted"><b>QQ：123456789</b></div>
                  </div>
                </div>
              </li>
              <li class="list-group-item">
                想要快速回答你的问题就请把问题描述讲清楚!<br>
                下单账号+业务名称+问题，直奔主题，按顺序回复!<br>
                有问题直接留言，请勿抖动语音否则直接无视。<br>
              </li>
            </ul>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="showCustomerServiceModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 简约提示弹窗模态框 (新版) -->
    <div class="simple-notification-overlay" v-show="showSimpleNotificationModal" @click="closeSimpleNotification">
      <div class="simple-notification-modal" @click.stop>
        <div class="simple-notification-header">
          <h4 class="simple-notification-title">提示</h4>
          <button type="button" class="close" @click="closeSimpleNotification">×</button>
        </div>
        <div class="simple-notification-body">
          [[ simpleNotificationMessage ]]
        </div>
        <div class="simple-notification-footer">
          <button type="button" class="btn btn-default" @click="closeSimpleNotification">确认</button>
        </div>
      </div>
    </div>

    <!-- 分站介绍模态框 -->
    <div class="modal fade" align="left" id="userjs" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showVersionModal" style="display: none;">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="list-group-item reed" style="background:linear-gradient(120deg, #FE2EF7 10%, #71D7A2 90%);">
            <button type="button" class="close" @click="showVersionModal = false">
              <span aria-hidden="true">×</span>
              <span class="sr-only">Close</span>
            </button>
            <div class="text-center">
              <h4 class="modal-title" id="myModalLabel">
                <b><span style="color:#fff">版本介绍</span></b>
              </h4>
            </div>
          </div>
          <div class="modal-body">
            <div class="table-responsive">
              <table class="table table-borderless table-vcenter">
                <thead>
                  <tr>
                    <th style="width: 100px;">功能</th>
                    <th class="text-center" style="width: 20px;">普及版/专业版</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="active">
                    <td>独立网站/专属后台</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="">
                    <td>低价拿货/调整价格</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="info">
                    <td>搭建分站/管理分站</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="">
                    <td>超低密价/高额提成</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="danger">
                    <td>赠送专属APP</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="showVersionModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增：支付二维码模态框 -->
    <div class="payment-modal-overlay" v-if="showPaymentQRCodeModal" @click="manualCloseQrCodeModal">
      <div class="payment-modal-card" @click.stop>
        <div class="payment-modal-header">
          <h4 class="payment-modal-title">请扫码支付</h4>
          <button type="button" class="payment-modal-close" @click="manualCloseQrCodeModal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="payment-modal-body">
          <!-- 支付进行中 -->
          <div v-if="paymentStatus === 'pending' || paymentStatus === 'paid'">
            <div class="payment-info-group">
              <div class="payment-method">
                <svg v-if="paymentQRCodeData.isAlipay" class="payment-icon alipay" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M620.544 587.264c54.272-98.304 73.728-192 73.728-192H512V326.144h216.576v-29.696H512V193.024h-98.816v103.424h-197.12v29.696h197.12v69.12H245.76v29.696h339.968c0 5.12 0 5.12-5.12 9.728 0 34.304-24.576 83.456-44.544 123.392-250.88-98.816-325.12-39.424-344.576-29.696-167.424 118.272-9.728 266.24 14.848 261.12 177.152 39.424 290.816-34.304 369.664-128 5.12 5.12 9.728 5.12 14.848 5.12 54.272 29.696 315.392 152.576 315.392 152.576v-147.968c-39.424-0.512-182.272-49.664-285.696-83.968z m-128 44.032c-123.392 157.696-270.848 108.544-295.424 98.304-58.88-14.848-78.848-123.392-5.12-157.696 123.392-39.424 231.424 5.12 310.272 44.544-5.12 10.24-9.728 14.848-9.728 14.848z" fill="currentColor"></path></svg>
                <i v-else :class="paymentQRCodeData.payment_method_icon" class="payment-icon"></i>
                <span class="payment-name">[[ paymentQRCodeData.payment_method_name ]]</span>
              </div>
              <div class="payment-price">
                <span class="currency-symbol">¥</span>[[ paymentQRCodeData.final_price.toFixed(2) ]]
              </div>
            </div>

            <div class="qrcode-wrapper">
              <div class="qrcode-box" v-if="paymentStatus === 'pending'">
                <div class="qrcode-loading-spinner" v-if="isQrCodeLoading">
                  <i class="fa fa-spinner fa-spin"></i>
                </div>
                <img :src="paymentQRCodeData.payment_url" @load="isQrCodeLoading = false" @error="isImageLoadedError = true" v-show="!isQrCodeLoading && !isImageLoadedError" alt="支付二维码">
                <div class="qrcode-error-tip" v-if="isImageLoadedError">
                  <i class="fa fa-exclamation-triangle"></i>
                  <span>二维码加载失败</span>
                </div>
              </div>
              <div class="payment-success-animation" v-if="paymentStatus === 'paid'">
                <svg class="checkmark" viewBox="0 0 52 52">
                  <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>
                  <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
                </svg>
                <p class="success-text">支付成功</p>
              </div>
            </div>

            <div class="payment-footer">
              <div class="countdown-timer" v-if="paymentStatus === 'pending'">
                请在 <span class="time-highlight">[[ formattedCountdown.minutes ]]:[[ formattedCountdown.seconds ]]</span> 内完成支付
              </div>
              <div class="auto-close-tip" v-if="paymentStatus === 'paid'">
                [[ closeModalCountdown ]] 秒后将自动关闭
              </div>
            </div>
          </div>

          <!-- 订单超时 -->
          <div class="payment-status-display" v-if="paymentStatus === 'expired'">
            <i class="fa fa-times-circle status-icon error"></i>
            <p class="status-text">订单已超时</p>
            <button class="btn-re-order" @click="reOrder">重新下单</button>
          </div>

          <!-- 订单取消 -->
          <div class="payment-status-display" v-if="paymentStatus === 'cancelled'">
            <i class="fa fa-ban status-icon cancelled"></i>
            <p class="status-text">订单已取消</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认订单模态框 -->
    <div class="info-modal-overlay" v-show="showConfirmOrderModal" @click="showConfirmOrderModal = false">
      <div class="order-confirm-modal-content" @click.stop>
        <div class="order-confirm-modal-header">
          <h4 class="info-modal-title">确认订单信息</h4>
          <button type="button" class="close" @click="showConfirmOrderModal = false">×</button>
        </div>
        <div class="order-confirm-modal-body">
          <div v-if="productDetails">
            <!-- 订单摘要 -->
            <div class="order-summary-card">
              <div class="summary-item">
                <strong>商品名称:</strong>
                <span>[[ productDetails.name ]]</span>
              </div>
              <div class="summary-item">
                <strong>单价:</strong>
                <span>¥[[ productDetails.price ]]</span>
              </div>
              <div class="summary-item">
                <strong>数量:</strong>
                <span>[[ orderQuantity ]]</span>
              </div>
              <div v-if="couponDetails" class="summary-item">
                <strong>优惠券:</strong>
                <span class="coupon-applied-badge">
                  <template v-if="couponDetails.discountType === 'fixed'">-  ¥[[ couponDetails.discountValue ]]</template>
                  <template v-if="couponDetails.discountType === 'percentage'">[[ couponDetails.discountValue ]]% OFF</template>
                </span>
              </div>
              <div class="summary-divider"></div>
              <div class="summary-total">
                <strong>应付总额:</strong>
                <span class="final-price-value">¥[[ finalPrice.toFixed(2) ]]</span>
              </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-selection-area">
              <h5>选择支付方式</h5>
              <div class="payment-layout-vertical">
                <!-- 垂直支付渠道Tab -->
                <div class="payment-tabs">
                  <div v-for="channel in paymentMethods" :key="channel.id"
                       class="payment-tab-item"
                       :class="{ active: selectedPaymentChannelId === channel.id }"
                       @click="selectedPaymentChannelId = channel.id">
                    <i :class="paymentIconMap[channel.icon] || 'fa fa-credit-card'" class="payment-icon"></i>
                    <span>[[ channel.name ]]</span>
                  </div>
                </div>
                <!-- 支付方式选项 -->
                <div class="payment-options">
                  <div v-if="selectedPaymentChannelId">
                    <div v-for="way in paymentMethods.find(c => c.id === selectedPaymentChannelId).data" :key="way"
                         class="payment-option-card"
                         :class="{ selected: selectedPaymentWay === way }"
                         @click="selectedPaymentWay = way">
                      <template v-if="['alipay', 'ailpay', 'alipay_wap', 'ailpay_wap'].includes(way.trim().toLowerCase())">
                        <svg class="icon payment-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="40" height="40"><path d="M620.544 587.264c54.272-98.304 73.728-192 73.728-192H512V326.144h216.576v-29.696H512V193.024h-98.816v103.424h-197.12v29.696h197.12v69.12H245.76v29.696h339.968c0 5.12 0 5.12-5.12 9.728 0 34.304-24.576 83.456-44.544 123.392-250.88-98.816-325.12-39.424-344.576-29.696-167.424 118.272-9.728 266.24 14.848 261.12 177.152 39.424 290.816-34.304 369.664-128 5.12 5.12 9.728 5.12 14.848 5.12 54.272 29.696 315.392 152.576 315.392 152.576v-147.968c-39.424-0.512-182.272-49.664-285.696-83.968z m-128 44.032c-123.392 157.696-270.848 108.544-295.424 98.304-58.88-14.848-78.848-123.392-5.12-157.696 123.392-39.424 231.424 5.12 310.272 44.544-5.12 10.24-9.728 14.848-9.728 14.848z" fill="currentColor"></path></svg>
                      </template>
                      <template v-else>
                        <i :class="paymentIconMap[way.trim().toLowerCase()]" class="payment-icon" :alt="way"></i>
                      </template>
                      <span>[[ paymentNameMap[way.trim().toLowerCase()] || way ]]</span>
                      <small v-if="paymentMethods.find(c => c.id === selectedPaymentChannelId).fee > 0">
                        手续费: [[ paymentMethods.find(c => c.id === selectedPaymentChannelId).fee ]]%
                      </small>
                    </div>
                  </div>
                  <div v-else class="payment-placeholder">
                    请先从左侧选择一个支付渠道
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="order-confirm-modal-footer">
          <button type="button" class="btn-cancel-order" @click="showConfirmOrderModal = false">取消</button>
          <button type="button" class="btn-confirm-order" @click="handleConfirmOrder" :disabled="isLoading">
            <span v-if="isLoading"><i class="fa fa-spinner fa-spin"></i> 提交中...</span>
            <span v-else>确认支付</span>
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import md5 from 'js-md5'

export default {
  name: 'RainbowShopSimple',
  filters: {
    twoDigits(value) {
      if (value === undefined || value === null) return '00';
      const s = value.toString();
      return s.length > 1 ? s : '0' + s;
    }
  },
  data() {
    return {
      // 模态框控制
      showModal: false,
      showAnnounceModal: false,
      showCustomerServiceModal: false,
      showVersionModal: false,
      showSimpleNotificationModal: false,
      simpleNotificationMessage: '',

      // Tab控制
      activeTab: 'shop',

      // 下单相关
      searchKeyword: '',
      selectedCategoryL1: '',
      selectedCategoryL2: '',
      selectedProduct: '',
      productPrice: '',
      stockCount: '',
      orderQuantity: 1,
      showCategoryL2: false,
      showProductSelect: false,
      isSearchMode: false,

      // 查询相关
      searchQuery: '',
      showQueryResult: false,
      queryResults: [
        {
          id: 'ORD20250731001',
          account: '<EMAIL>',
          productName: 'QQ会员充值',
          quantity: 1,
          createTime: '2025-07-31 14:30:25',
          status: 'completed',
          statusText: '已完成',
          amount: '15.00'
        }
      ],

      // 背景图片
      backgroundImageUrl: 'https://imgapi.xl0408.top/index.php',

      // 时间提示框相关
      currentTime: '',
      tipStyle: 'default', // default, success, warning, info
      tipAnimation: 'tada', // tada, bounce, pulse, flash
      showTip: true,
      timeTimer: null,

      // 用户认证信息
      userId: '',
      userKey: '',
      token: '',

      // 分类数据
      categoriesData: [],

      // 商品数据
      productsData: [],

      // 当前选中的子分类数据
      currentSubCategories: [],

      // 商品详情
      productDetails: null,
      dynamicInputs: [],
      email: '',
      inputValues: {},
      showInfoModal: false,
      currentYear: '',
      currentHost: '',
      useCoupon: false,
      couponCode: '',

      // 新增：确认订单模态框相关
      showConfirmOrderModal: false,
      paymentMethods: [],
      selectedPaymentChannelId: null,
      selectedPaymentWay: null,
      couponDetails: null,
      finalPrice: 0,
      isLoading: false,

      // 新增：支付二维码模态框相关
      showPaymentQRCodeModal: false,
      paymentQRCodeData: { isAlipay: false },
      paymentCountdown: 120,
      paymentStatus: 'pending', // pending, paid, expired, cancelled
      statusCheckTimer: null,
      countdownTimer: null,
      closeModalTimer: null,
      closeModalCountdown: 4,
      isImageLoadedError: false,
      isQrCodeLoading: true, // 新增：用于控制二维码加载动画的状态
      
      // 新增：支付方式图标映射
      paymentIconMap: {
        wxpay: 'fa fa-weixin',
        wechat_h5: 'fa fa-weixin',
        qqpay: 'fa fa-qq',
        balance: 'fa fa-money'
      },
      paymentNameMap: {
        wxpay: '微信支付',
        wechat_h5: '微信支付',
        alipay: '支付宝',
        ailpay: '支付宝',
        alipay_wap: '支付宝',
        ailpay_wap: '支付宝',
        qqpay: 'QQ钱包',
        balance: '余额支付'
      }
    }
  },

  computed: {
    formattedCountdown() {
      const minutes = Math.floor(this.paymentCountdown / 60);
      const seconds = this.paymentCountdown % 60;
      return {
        minutes: String(minutes).padStart(2, '0'),
        seconds: String(seconds).padStart(2, '0')
      };
    },
    // 根据选中的一级分类获取对应的子分类
    getSubCategoriesByParentId() {
      if (!this.selectedCategoryL1 || !this.categoriesData.length) {
        return [];
      }
      const parentCategory = this.categoriesData.find(cat => cat.id == this.selectedCategoryL1);
      return parentCategory ? parentCategory.children || [] : [];
    },
    stockColor() {
      if (!this.productDetails) {
        return '';
      }
      const stock = parseInt(this.productDetails.stock, 10);
      if (stock < 10) {
        return 'stock-low';
      } else if (stock < 50) {
        return 'stock-medium';
      } else {
        return 'stock-high';
      }
    }
  },
  methods: {
    isValidEmail(email) {
      const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(String(email).toLowerCase());
    },
    // 初始化数据加载
    async loadInitialData() {
      try {
        const success = await this.getCategoriesData();

        if (success) {
          // 分类数据加载成功
        } else {
          this.showSimpleNotification('分类数据加载失败，请刷新页面重试');
        }
      } catch (error) {
        this.showSimpleNotification('初始化失败，请刷新页面重试');
      }
    },

    // 简约弹窗相关方法
    showSimpleNotification(message) {
      this.simpleNotificationMessage = message;
      this.showSimpleNotificationModal = true;
    },

    closeSimpleNotification() {
      this.showSimpleNotificationModal = false;
      this.simpleNotificationMessage = '';
    },

    // 基本交互方法
    increaseQuantity() {
      this.orderQuantity++;
    },

    decreaseQuantity() {
      if (this.orderQuantity > 1) {
        this.orderQuantity--;
      }
    },

    // 新的购买流程
    async buyNow() {
      if (this.isLoading) return;
      this.isLoading = true;

      // 1. 前端验证
      if (!this.email) {
        this.showSimpleNotification('请输入收货邮箱');
        this.isLoading = false;
        return;
      }
      if (!this.isValidEmail(this.email)) {
        this.showSimpleNotification('收货邮箱格式错误');
        this.isLoading = false;
        return;
      }
      for (const input of this.dynamicInputs) {
        if (!this.inputValues[input.name]) {
          this.showSimpleNotification(`请输入${input.name}`);
          this.isLoading = false;
          return;
        }
      }

      // 2. 优惠券验证
      this.couponDetails = null; // 重置优惠券信息
      if (this.useCoupon && this.couponCode) {
        const coupon = await this.getCouponDetails(this.couponCode);
        if (!coupon) {
          this.isLoading = false;
          return; // getCouponDetails内部会显示错误
        }
        this.couponDetails = coupon;
      }

      // 3. 获取支付方式
      const methods = await this.getPaymentMethods();
      if (!methods || methods.length === 0) {
        this.showSimpleNotification('暂无可用支付方式');
        this.isLoading = false;
        return;
      }
      this.paymentMethods = methods;
      
      // 新增逻辑：自动选择第一个支付渠道和方式
      if (methods && methods.length > 0) {
        this.selectedPaymentChannelId = methods.id;
        if (methods.data && methods.data.length > 0) {
          this.selectedPaymentWay = methods.data;
        } else {
          this.selectedPaymentWay = null;
        }
      } else {
        this.selectedPaymentChannelId = null;
        this.selectedPaymentWay = null;
      }

      // 4. 计算价格并显示模态框
      let price = parseFloat(this.productDetails.price) * this.orderQuantity;
      if (this.couponDetails) {
        if (this.couponDetails.discountType === 'fixed') {
          price -= parseFloat(this.couponDetails.discountValue);
        } else if (this.couponDetails.discountType === 'percentage') {
          price *= (1 - parseFloat(this.couponDetails.discountValue) / 100);
        }
      }
      this.finalPrice = Math.max(0, price); // 确保价格不为负

      this.showConfirmOrderModal = true;
      this.isLoading = false;
    },

    // 处理确认订单
    async handleConfirmOrder() {
      if (!this.selectedPaymentChannelId || !this.selectedPaymentWay) {
        this.showSimpleNotification('请选择支付方式');
        return;
      }

      this.isLoading = true;

      const attach = this.dynamicInputs.map(input => ({
        name: input.name,
        value: this.inputValues[input.name]
      }));

      const orderData = {
        userId: this.userId,
        id: this.selectedProduct,
        mail: this.email,
        attach: attach,
        payment: {
          id: this.selectedPaymentChannelId,
          way: this.selectedPaymentWay
        },
        time: Date.now().toString()  // 十三位时间戳
      };

      if (this.couponDetails) {
        orderData.coupon = this.couponCode;
      }

      const result = await this.submitOrder(orderData);
      this.isLoading = false;

      if (result) {
        if (result.payment_type === 'url') {
          window.location.href = result.payment_url;
        } else if (result.payment_type === 'api') {
          this.showConfirmOrderModal = false;
          const selectedChannel = this.paymentMethods.find(c => c.id === this.selectedPaymentChannelId);
          const isAlipay = ['alipay', 'ailpay', 'alipay_wap', 'ailpay_wap'].includes(this.selectedPaymentWay.trim().toLowerCase());
          
          this.paymentQRCodeData = {
            order_id: result.order_id,
            payment_url: result.payment_url,
            final_price: this.finalPrice,
            payment_method_name: this.paymentNameMap[this.selectedPaymentWay.trim().toLowerCase()] || this.selectedPaymentWay,
            payment_method_icon: this.paymentIconMap[this.selectedPaymentWay.trim().toLowerCase()] || 'fa fa-credit-card',
            isAlipay: isAlipay,
          };
          this.paymentStatus = 'pending';
          this.paymentCountdown = 120;
          this.isImageLoadedError = false;
          this.isQrCodeLoading = true; // 重置加载状态
          this.showPaymentQRCodeModal = true;
          this.startPaymentProcess();
        }
      }
    },

    // 重新下单方法
    reOrder() {
      this.closeQrCodeModal();
      // 重新打开确认订单模态框，让用户可以重新选择支付方式或修改信息
      this.showConfirmOrderModal = true;
    },

    // 新增：支付流程相关函数
    startPaymentProcess() {
      this.stopPaymentProcess(); // 确保没有旧的计时器在运行
      this.countdownTimer = setInterval(() => {
        if (this.paymentCountdown > 0) {
          this.paymentCountdown--;
        } else {
          // 倒计时结束，不再直接改变状态，而是调用关闭模态框的函数并传递超时标志
          this.closeQrCodeModal(true);
        }
      }, 1000);

      this.statusCheckTimer = setInterval(() => {
        this.checkPaymentStatus();
      }, 3000);
    },

    async checkPaymentStatus() {
      if (!this.paymentQRCodeData.order_id) return;
      try {
        const response = await fetch(`/user/api/check_payment_status/?order=${this.paymentQRCodeData.order_id}`);
        if (!response.ok) {
          if (response.status >= 500) {
            this.stopPaymentProcess();
          }
          return;
        }
        const data = await response.json();
        if (data.code === 200 && data.data) {
          const status = data.data.status;
          if (status === 'paid' || status === 'finish') {
            this.paymentStatus = 'paid';
          } else if (status === 'cancelled') {
            this.paymentStatus = 'cancelled';
            this.stopPaymentProcess();
            this.showSimpleNotification('支付订单已取消');
          }
        }
      } catch (error) {
        console.log('检查支付状态失败: ', error);
        
      }
    },

    stopPaymentProcess() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      if (this.statusCheckTimer) {
        clearInterval(this.statusCheckTimer);
        this.statusCheckTimer = null;
      }
      if (this.closeModalTimer) {
        clearInterval(this.closeModalTimer);
        this.closeModalTimer = null;
      }
    },

    handlePaymentSuccess() {
      this.stopPaymentProcess();
      this.closeModalCountdown = 4;
      this.closeModalTimer = setInterval(() => {
        if (this.closeModalCountdown > 0) {
          this.closeModalCountdown--;
        } else {
          this.closeQrCodeModal();
        }
      }, 1000);
    },

    closeQrCodeModal(isTimeout = false) {
      this.showPaymentQRCodeModal = false;
      this.stopPaymentProcess();

      // 仅在超时关闭时显示超时提示
      if (isTimeout) {
        this.paymentStatus = 'expired'; // 更新状态以显示超时信息（如果用户再次打开）
        this.showSimpleNotification('订单已超时，请重新下单');
      }
    },

    manualCloseQrCodeModal() {
      this.showPaymentQRCodeModal = false;
      this.stopPaymentProcess();
      // 手动关闭不显示任何提示
    },

    // 查询方法（简化版）
    submitQuery() {
      this.showQueryResult = true;
      if (!this.searchQuery.trim()) {
        // 如果没有输入订单号，显示示例数据
        this.queryResults = [
          {
            id: 'ORD20250731001',
            account: '<EMAIL>',
            productName: 'QQ会员充值',
            quantity: 1,
            createTime: '2025-07-31 14:30:25',
            status: 'completed',
            statusText: '已完成',
            amount: '15.00'
          }
        ];
      } else {
        // 根据输入显示对应结果或空结果
        if (this.searchQuery === 'ORD20250731001') {
          this.queryResults = [
            {
              id: 'ORD20250731001',
              account: '<EMAIL>',
              productName: 'QQ会员充值',
              quantity: 1,
              createTime: '2025-07-31 14:30:25',
              status: 'completed',
              statusText: '已完成',
              amount: '15.00'
            }
          ];
        } else {
          this.queryResults = [];
        }
      }
    },

    // 时间提示框相关方法
    updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      this.currentTime = `［${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}］当前系统时间`;
    },

    startTimeTimer() {
      this.updateCurrentTime(); // 立即更新一次
      this.timeTimer = setInterval(() => {
        this.updateCurrentTime();
      }, 1000);
    },

    stopTimeTimer() {
      if (this.timeTimer) {
        clearInterval(this.timeTimer);
        this.timeTimer = null;
      }
    },

    // 切换提示框样式
    changeTipStyle() {
      const styles = ['default', 'success', 'warning', 'info'];
      const currentIndex = styles.indexOf(this.tipStyle);
      this.tipStyle = styles[(currentIndex + 1) % styles.length];
    },

    // 切换动画效果
    changeTipAnimation() {
      const animations = ['tada', 'bounce', 'pulse', 'flash'];
      const currentIndex = animations.indexOf(this.tipAnimation);
      this.tipAnimation = animations[(currentIndex + 1) % animations.length];
    },

    // 切换提示框显示状态
    toggleTip() {
      this.showTip = !this.showTip;
    },

    // 点击提示框的处理方法
    onTipClick() {
      this.changeTipStyle();
      // 重新触发动画
      const tipElement = document.querySelector('.shuaibi-tip');
      if (tipElement) {
        tipElement.classList.remove('animated', this.tipAnimation);
        setTimeout(() => {
          tipElement.classList.add('animated', this.tipAnimation);
        }, 10);
      }
    },

    // MD5签名生成函数
    generateSignature(userId, userKey) {
      if (!userId || !userKey) {
        return '';
      }
      try {
        return md5(userId + userKey);
      } catch (error) {
        return '';
      }
    },

    // 获取用户信息函数
    async getUserInfo() {
      try {
        // 检查本地存储中是否已有用户信息
        const storedUserId = localStorage.getItem('userId');
        const storedUserKey = localStorage.getItem('userKey');
        const storedToken = localStorage.getItem('token');

        if (storedUserId && storedUserKey && storedToken) {
          this.userId = storedUserId;
          this.userKey = storedUserKey;
          this.token = storedToken;
          return true;
        }

        // 如果没有，则调用GetUser接口获取
        const response = await fetch('/user/api/GetUser/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP请求错误! 状态: ${response.status}`);
        }

        const data = await response.json();
        const token = response.headers.get('token');

        if (data && data.user && token) {
          this.userId = data.user.id.toString();
          this.userKey = data.user.user_key;
          this.token = token;

          // 保存到本地存储
          localStorage.setItem('userId', this.userId);
          localStorage.setItem('userKey', this.userKey);
          localStorage.setItem('token', this.token);

          return true;
        } else {
          throw new Error('获取用户信息失败');
        }
      } catch (error) {
        this.showSimpleNotification('获取用户信息失败，请刷新页面重试');
        return false;
      }
    },

    // 获取分类数据函数
    async getCategoriesData() {
      try {
        // 确保用户信息已获取
        const hasUserInfo = await this.getUserInfo();
        if (!hasUserInfo) {
          return false;
        }

        // 生成签名
        const sign = this.generateSignature(this.userId, this.userKey);

        // 构建请求参数
        const formData = new FormData();
        formData.append('userId', this.userId);
        formData.append('sign', sign);

        // 发送请求
        const response = await fetch('/user/api/categories/', {
          method: 'POST',
          body: formData,
          headers: {
            'Token': this.token
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP请求错误! 状态: ${response.status}`);
        }

        const data = await response.json();

        if (data.code === 200 && data.data) {
          this.categoriesData = data.data;
          return true;
        } else {
          throw new Error(data.message || '获取分类数据失败');
        }
      } catch (error) {
        this.showSimpleNotification('获取分类数据失败，请刷新页面重试');
        return false;
      }
    },

    // 获取商品列表函数（支持分页和搜索）
    async getProductListData(categoryId, getAllPages = true, searchName = null) {
      try {
        // 确保用户信息已获取
        const hasUserInfo = await this.getUserInfo();
        if (!hasUserInfo) {
          return [];
        }

        let allProducts = [];
        let currentPage = 1;
        let hasNextPage = true;

        while (hasNextPage) {
          const formData = new FormData();
          let signData = '';

          if (searchName) {
            // 搜索模式
            signData = searchName + this.userId + this.userKey;
            formData.append('name', searchName);
          } else {
            // 分类模式
            signData = categoryId + this.userId + this.userKey;
            formData.append('categoryId', categoryId);
          }

          const sign = md5(signData);
          formData.append('userId', this.userId);
          formData.append('sign', sign);
          formData.append('page', currentPage);
          formData.append('page_size', 100); // 使用最大页面大小

          // 发送请求
          const response = await fetch('/user/api/productlist/', {
            method: 'POST',
            body: formData,
            headers: {
              'Token': this.token
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP请求错误! 状态: ${response.status}`);
          }

          const data = await response.json();

          if (data.code === 200 && data.data && data.data.items) {
            allProducts = allProducts.concat(data.data.items);

            // 检查是否还有下一页
            if (getAllPages && data.data.pagination && data.data.pagination.has_next) {
              currentPage++;
            } else {
              hasNextPage = false;
            }
          } else {
            // 如果API返回空数据，也认为是有效的，只是没有结果
            if (data.code === 200 && data.data && data.data.items.length === 0) {
              hasNextPage = false;
            } else {
              throw new Error(data.msg || '获取商品列表失败');
            }
          }
        }

        return allProducts;
      } catch (error) {
        this.showSimpleNotification('获取商品列表失败，请重试');
        return [];
      }
    },

    // 搜索按钮点击处理
    async handleSearchClick() {
      if (!this.searchKeyword.trim()) {
        this.showSimpleNotification('请输入搜索关键词');
        return;
      }

      const products = await this.getProductListData(null, true, this.searchKeyword.trim());

      if (products && products.length > 0) {
        this.isSearchMode = true;
        this.productsData = products;
        this.selectedProduct = '';
        this.showProductSelect = true;
        // 隐藏分类选择
        this.showCategoryL2 = false;
      } else {
        this.showSimpleNotification('未搜索到相关商品');
        this.isSearchMode = false;
        this.productsData = [];
        this.showProductSelect = false;
      }
    },

    // 一级分类选择变化处理
    async onCategoryL1Change() {
      // 退出搜索模式
      this.isSearchMode = false;
      // 重置下级选择
      this.selectedCategoryL2 = '';
      this.selectedProduct = '';
      this.productsData = [];
      this.showProductSelect = false;

      if (this.selectedCategoryL1) {
        // 获取对应的子分类
        const parentCategory = this.categoriesData.find(cat => cat.id == this.selectedCategoryL1);

        if (parentCategory && parentCategory.children && parentCategory.children.length > 0) {
          this.currentSubCategories = parentCategory.children;
          this.showCategoryL2 = true;
        } else {
          this.showCategoryL2 = false;
          this.currentSubCategories = [];
          // 显示弹窗提示该分类不存在下级分类
          setTimeout(() => {
            this.showSimpleNotification('该分类不存在下级分类');
          }, 100);
        }
      } else {
        this.showCategoryL2 = false;
        this.currentSubCategories = [];
      }
    },

    // 二级分类选择变化处理
    async onCategoryL2Change() {
      // 重置商品选择
      this.selectedProduct = '';
      this.productsData = [];

      if (this.selectedCategoryL2) {
        // 获取该分类下的商品列表
        const products = await this.getProductListData(this.selectedCategoryL2, true);

        if (products && products.length > 0) {
          this.productsData = products;
          this.showProductSelect = true;
        } else {
          this.showProductSelect = false;
          // 显示弹窗提示该分类下暂无商品
          setTimeout(() => {
            this.showSimpleNotification('该分类下暂无商品');
          }, 100);
        }
      } else {
        this.showProductSelect = false;
      }
    },

    // 获取优惠券详情函数
    async getCouponDetails(couponCode) {
      try {
        const hasUserInfo = await this.getUserInfo();
        if (!hasUserInfo) {
          throw new Error('无法获取用户信息');
        }

        const signData = couponCode + this.userId + this.userKey;
        const sign = md5(signData);

        const response = await fetch(`/user/api/couponsDetail/?coupon=${couponCode}&userId=${this.userId}&sign=${sign}`, {
          method: 'GET',
          headers: {
            'Token': this.token
          }
        });

        if (!response.ok) {
          // 如果能解析出JSON错误信息，就使用它
          try {
            const errorData = await response.json();
            throw new Error(errorData.msg || `HTTP错误! 状态: ${response.status}`);
          } catch (e) {
            throw new Error(`HTTP错误! 状态: ${response.status}`);
          }
        }

        const data = await response.json();

        if (data.code === 200) {
          return data.data;
        } else {
          throw new Error(data.msg || '提交订单失败');
        }
      } catch (error) {
        this.showSimpleNotification(error.message);
        // 清空优惠券码，因为它是无效的
        this.couponCode = '';
        return null;
      }
    },

    // 获取支付方式函数
    async getPaymentMethods() {
      try {
        const hasUserInfo = await this.getUserInfo();
        if (!hasUserInfo) {
          throw new Error('无法获取用户信息');
        }

        const sign = md5(this.userId + this.userKey);

        const formData = new FormData();
        formData.append('userId', this.userId);
        formData.append('sign', sign);

        const response = await fetch('/user/api/payment_methods/', {
          method: 'POST',
          headers: {
            'Token': this.token
          },
          body: formData
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.msg || `HTTP错误! 状态: ${response.status}`);
        }

        const data = await response.json();

        if (data.code === 200) {
          return data.data;
        } else {
          throw new Error(data.msg || '获取支付方式失败');
        }
      } catch (error) {
        this.showSimpleNotification(error.message);
        return null;
      }
    },

    // 提交订单函数
    async submitOrder(orderData) {
      try {
        const hasUserInfo = await this.getUserInfo();
        if (!hasUserInfo) {
          throw new Error('无法获取用户信息');
        }

        // 准备用于签名的纯数据对象
        const dataToSign = { ...orderData };
        
        // 1. 按键名ASCII排序
        const sortedData = Object.keys(dataToSign)
          .sort()
          .reduce((obj, key) => {
            obj[key] = dataToSign[key];
            return obj;
          }, {});

        // 2. 转换为紧凑的JSON字符串
        const jsonString = JSON.stringify(sortedData);

        // 3. 拼接user_key
        const stringToSign = jsonString + this.userKey;
        
        // 4. MD5加密
        const sign = md5(stringToSign);

        // 准备最终的请求体
        const finalPayload = {
          ...sortedData,
          sign: sign
        };

        const response = await fetch('/user/api/submit_order/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Token': this.token
          },
          body: JSON.stringify(finalPayload)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.msg || `HTTP错误! 状态: ${response.status}`);
        }

        const data = await response.json();

        if (data.code === 200) {
          return data.data;
        } else {
          throw new Error(data.msg || '提交订单失败');
        }
      } catch (error) {
        this.showSimpleNotification(error.message);
        return null;
      }
    },

    // 获取商品详细信息函数
    async getProductDetails(productId) {
      // 重置旧数据
      this.productDetails = null;
      this.dynamicInputs = [];
      this.inputValues = {};
      this.productPrice = '';
      this.stockCount = '';

      if (!productId) {
        return;
      }

      try {
        // 确保用户信息已获取
        const hasUserInfo = await this.getUserInfo();
        if (!hasUserInfo) {
          return;
        }

        // 生成签名
        const signData = productId + this.userId + this.userKey;
        const sign = md5(signData);

        // 构建请求参数
        const formData = new FormData();
        formData.append('id', productId);
        formData.append('userId', this.userId);
        formData.append('sign', sign);

        // 发送请求
        const response = await fetch('/user/api/productInfo/', {
          method: 'POST',
          body: formData,
          headers: {
            'Token': this.token
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP请求错误! 状态: ${response.status}`);
        }

        const data = await response.json();

        if (data.code === 200 && data.data && data.data.length > 0) {
          const productObject = data.data[0];
          
          this.productDetails = productObject;
          this.dynamicInputs = this.productDetails.attach || [];
          // 初始化inputValues
          this.inputValues = this.dynamicInputs.reduce((acc, curr) => {
            acc[curr.name] = '';
            return acc;
          }, {});
        } else {
          throw new Error(data.msg || '获取商品详情失败');
        }
      } catch (error) {
        this.showSimpleNotification(error.message || '获取商品详情失败，请重试');
        // 清理数据
        this.productDetails = null;
      }
    }
  },

  // 监听器
  watch: {
    selectedCategoryL1(newVal) {
      if (newVal) {
        this.showCategoryL2 = true;
        this.selectedCategoryL2 = '';
        this.selectedProduct = '';
        this.showProductSelect = false;
      } else {
        this.showCategoryL2 = false;
        this.showProductSelect = false;
      }
    },

    selectedCategoryL2(newVal) {
      if (newVal) {
        this.showProductSelect = true;
        this.selectedProduct = '';
      } else {
        this.showProductSelect = false;
      }
    },

    selectedProduct(newVal) {
      if (newVal) {
        this.getProductDetails(newVal);
      } else {
        // 清空商品详情
        this.productDetails = null;
        this.dynamicInputs = [];
        this.inputValues = {};
        this.productPrice = '';
        this.stockCount = '';
      }
    },
    paymentStatus(newStatus) {
      if (newStatus === 'paid') {
        this.handlePaymentSuccess();
      }
    }
  },

  async mounted() {
    // 初始化
    // 启动时间定时器
    this.startTimeTimer();

    // 设置当前年份和域名
    this.currentYear = new Date().getFullYear();
    this.currentHost = window.location.host;

    // 加载分类数据
    await this.loadInitialData();

    // 添加F12和右键禁用逻辑
    if (window.location.host !== 'localhost:8000') {
      this.handleKeydown = (e) => {
        if (e.keyCode === 123) {
          e.preventDefault();
        }
      };
      this.handleContextmenu = (e) => {
        e.preventDefault();
      };
      window.addEventListener('keydown', this.handleKeydown);
      window.addEventListener('contextmenu', this.handleContextmenu);
    }
  },

  // 组件销毁前清理定时器
  beforeDestroy() {
    this.stopTimeTimer();
    // 移除事件监听器
    if (window.location.host !== 'localhost:8000') {
      window.removeEventListener('keydown', this.handleKeydown);
      window.removeEventListener('contextmenu', this.handleContextmenu);
    }
  }
}
</script>

<style>
/* 导入外部CSS框架 */
@import url('//cdn.staticfile.org/twitter-bootstrap/3.3.7/css/bootstrap.min.css');
@import url('//cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

/* 全局样式 */
body {
  background: #ffffff;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #454e59;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 主容器样式 */
.main-container {
  min-height: 100vh;
  padding: 10px 0;
  background: rgba(248, 249, 250, 0.3);
  backdrop-filter: blur(1px);
}

/* 自定义提示框样式 */
.shuaibi-tip {
  background: #dc3545;
  color: white;
  margin: 8px 0;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  height: auto;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all 0.3s ease;
  user-select: none;
}

.shuaibi-tip:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 不同样式状态 */
.shuaibi-tip.tip-default {
  background: #dc3545;
}

.shuaibi-tip.tip-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.shuaibi-tip.tip-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
}

.shuaibi-tip.tip-info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.shuaibi-tip .label-danger {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

/* 动画效果增强 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-30px,0);
  }
  70% {
    transform: translate3d(0,-15px,0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

@keyframes pulse {
  0% {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.05, 1.05, 1.05);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}

.animated.bounce {
  animation: bounce 1s;
}

.animated.pulse {
  animation: pulse 1s;
}

.animated.flash {
  animation: flash 1s;
}

/* 全屏背景效果 */
.full-bg {
  min-height: 100vh;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #ffffff;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: -1;
}

/* 背景图片上的轻微白色遮罩 */
.full-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

/* Widget组件样式 */
.widget {
  background-color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  position: relative;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(233, 236, 239, 0.8);
}

.widget-content {
  padding: 12px;
}

/* Logo区域样式 */
.logo-header {
  background: #007bff;
  padding: 15px;
}

.logo-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50px;
}

.logo-title {
  padding: 0px;
  margin-top: 5px;
  background: white;
}

.logo-title h2 {
  margin: 5px 0 0 0;
}

.brand-link {
  color: #333;
  text-decoration: none;
  font-size: 18px;
}

.brand-link:hover {
  color: #007bff;
  text-decoration: none;
}

.brand-subtitle {
  color: #6c757d;
  font-size: 12px;
  margin: 3px 0 0 0;
  padding-bottom: 5px;
}

/* 现代化按钮样式 */
.action-buttons {
  padding: 8px;
  background: #f8f9fa;
}

.modern-btn-group {
  display: flex;
  gap: 12px;
}

.modern-btn-group .btn-group {
  flex: 1;
}

.btn-modern {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  text-decoration: none;
  color: #495057;
  font-size: 14px;
  width: 100%;
  height: 48px;
  justify-content: center;
}

.btn-modern:hover {
  background: #007bff;
  color: white;
  text-decoration: none;
}

.btn-modern i {
  font-size: 16px;
  margin-right: 8px;
}



/* Block组件样式 */
.block {
  margin: 0 0 12px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(233, 236, 239, 0.8);
  overflow: hidden;
}

.block.full2 {
  padding: 0;
}

.block-title {
  margin: 0;
  border-bottom: none;
  background: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.modern-nav-tabs {
  padding: 0;
  margin: 0;
  border-bottom: none;
  display: flex;
  background: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.modern-nav-tabs .nav-item {
  flex: 1;
  list-style: none;
}

.modern-nav-tabs .nav-link {
  border: none;
  padding: 8px 8px;
  margin: 0;
  border-radius: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  color: #6c757d;
  font-size: 14px;
  background: transparent;
  height: 48px;
  justify-content: center;
  outline: none;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modern-nav-tabs .nav-link:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px rgba(58, 141, 255, 0.2);
  background: rgba(58, 141, 255, 0.05);
}

.modern-nav-tabs .nav-link:hover {
  background: rgba(58, 141, 255, 0.08);
  color: #495057;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(58, 141, 255, 0.15);
}

.modern-nav-tabs .nav-link i {
  font-size: 16px;
}

.modern-nav-tabs .nav-item.active .nav-link {
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(58, 141, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.modern-nav-tabs .nav-item.active .nav-link:hover {
  background: linear-gradient(135deg, #0072ff 0%, #0041a3 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(58, 141, 255, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.nav-link-special {
  color: #dc3545 !important;
}

.modern-nav-tabs .nav-link-special:focus {
  outline: none !important;
  box-shadow: inset 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
  background: rgba(220, 53, 69, 0.05) !important;
}

.modern-nav-tabs .nav-link-special:hover {
  background: rgba(220, 53, 69, 0.08) !important;
  color: #dc3545 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
}

.modern-nav-tabs .nav-item.active .nav-link-special {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.modern-nav-tabs .nav-item.active .nav-link-special:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

/* Tab内容样式 */
.tab-content {
  padding: 8px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 6px 6px;
  min-height: 200px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* 现代化下单表单样式 */
.modern-order-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px 20px;
  margin: 12px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(233, 236, 239, 0.6);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modern-order-form:hover {
  box-shadow: 0 6px 20px rgba(58, 141, 255, 0.12);
  border-color: rgba(58, 141, 255, 0.25);
  transform: translateY(-2px);
}



/* 现代化表单组样式 */
.modern-order-form .form-group {
  margin-bottom: 12px;
  position: relative;
}

.modern-order-form .form-group:last-child {
  margin-bottom: 0;
}

/* 为输入组添加悬停效果 */
.modern-order-form .input-group:hover .input-group-addon {
  background: linear-gradient(135deg, #4dabf7 0%, #3a8dff 100%);
  box-shadow: 0 2px 8px rgba(58, 141, 255, 0.2);
}

.modern-order-form .input-group:hover .form-control {
  border-color: #007bff;
  box-shadow: 0 2px 10px rgba(58, 141, 255, 0.12);
}

/* 输入组整体样式优化 */
.modern-order-form .input-group {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 6px;
  overflow: hidden;
}

/* 确保select元素高度一致 */
.input-group select.form-control {
  height: 36px !important;
  padding: 0 14px !important;
  line-height: 1.2 !important;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px 12px;
  padding-right: 40px !important;
}

/* 确保数量调节按钮高度一致 */
.input-group-btn .btn {
  height: 36px !important;
  padding: 0 12px !important;
  border-radius: 0 !important;
  line-height: 1.2 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 表单样式 */
.form-group {
  margin-bottom: 8px;
}

.form-control {
  padding: 6px 10px;
  max-width: 100%;
  margin: 1px 0;
  color: #454e59;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  height: 32px;
}

.form-control:focus {
  border-color: #007bff;
  outline: none;
}

.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
  width: 100%;
  margin-bottom: 8px;
}

.input-group-addon {
  padding: 0 16px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: white;
  text-align: center;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  border: 1px solid transparent;
  border-radius: 6px;
  white-space: nowrap;
  display: table-cell;
  vertical-align: middle;
  width: 1%;
  height: 36px;
  box-shadow: 0 2px 6px rgba(58, 141, 255, 0.25);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
  display: table-cell;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  background: white;
  padding: 0 14px;
  font-size: 14px;
  height: 36px;
  line-height: 1.2;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.input-group .form-control:focus {
  border-color: #007bff;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(58, 141, 255, 0.12), 0 3px 12px rgba(58, 141, 255, 0.15);
}

.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.input-group-addon:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.input-group .form-control:first-child,
.input-group-addon:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.input-group-addon:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.input-group .form-control:last-child,
.input-group-addon:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* 按钮样式 */
.btn {
  border-radius: 4px;
  padding: 6px 16px;
  font-size: 14px;
  border: none;
  height: 32px;
  line-height: 20px;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  border: none;
  box-shadow: 0 3px 10px rgba(58, 141, 255, 0.25);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  font-weight: 400;
  letter-spacing: 0.3px;
}

.btn-primary:hover,
.btn-primary:focus {
  background: linear-gradient(135deg, #0072ff 0%, #0041a3 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(58, 141, 255, 0.35);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover,
.btn-success:focus {
  background: #218838;
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-info:hover,
.btn-info:focus {
  background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover,
.btn-warning:focus {
  background: #e0a800;
  color: #212529;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover,
.btn-danger:focus {
  background: #c82333;
  color: white;
}

.btn-block {
  width: 100%;
  margin-bottom: 6px;
  display: block;
  box-sizing: border-box;
}

/* 确保立即购买按钮与input-group宽度一致 */
#submit_buy_new.btn-block {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  padding: 0;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0.3px;
  margin-top: 8px;
  text-align: center;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  line-height: 1;
}

#submit_buy_new.btn-block:hover,
#submit_buy_new.btn-block:focus {
  text-decoration: none;
}

#submit_buy_new.btn-block:active {
  transform: translateY(-1px);
}

.btn-group-justified .btn {
  margin: 0 3px;
}

.btn-group-justified .btn:first-child {
  margin-left: 0;
}

.btn-group-justified .btn:last-child {
  margin-right: 0;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal[v-show="true"] {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 15px;
  max-width: 500px;
}

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  background-clip: padding-box;
  outline: 0;
  overflow: hidden;
}

.modal-header {
  padding: 12px 15px;
  border-bottom: 1px solid #dee2e6;
}

.modal-body {
  position: relative;
  padding: 15px;
  max-height: 50vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 12px 15px;
  text-align: right;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
}

.modal-footer .btn {
  margin-left: 6px;
}

/* 面板样式 */
.panel {
  margin-bottom: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(222, 226, 230, 0.8);
  border-radius: 6px;
  overflow: hidden;
}

.panel-primary > .panel-heading {
  color: #fff;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  border: none;
  box-shadow: 0 2px 6px rgba(58, 141, 255, 0.25);
}

.panel-heading {
  padding: 8px 15px;
  border-bottom: 1px solid #dee2e6;
  border-radius: 6px 6px 0 0;
  height: 40px;
  display: flex;
  align-items: center;
}

.panel-heading .panel-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.panel-body {
  padding: 12px;
}

/* 底部面板样式优化 */
.panel.panel-default .panel-body {
  text-align: center;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
}

.panel.panel-default .panel-body a {
  color: #007bff;
  text-decoration: none;
}

.panel.panel-default .panel-body a:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* 数据统计面板样式 */
.stats-panel {
  margin-bottom: 12px;
}

.stats-header {
  padding: 6px 12px;
  height: 32px;
  display: flex;
  align-items: center;
}

.stats-header .panel-title {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  color: white;
}

.stats-header i {
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 6px 6px;
}

.stats-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  padding: 16px 8px;
  text-align: center;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
  border-radius: 12px;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 4px 12px rgba(58, 141, 255, 0.15);
  border: 1px solid rgba(58, 141, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-item:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(58, 141, 255, 0.25);
  border-color: rgba(58, 141, 255, 0.3);
}

.stats-item:hover::before {
  opacity: 1;
}

.stats-value-row {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 3px;
  margin-bottom: 4px;
}

.stats-number {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(58, 141, 255, 0.1);
}

.stats-unit {
  font-size: 12px;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  opacity: 0.8;
}

.stats-label {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.2;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 客服信息卡片样式 */
.customer-service-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(233, 236, 239, 0.8);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.customer-service-card:hover {
  box-shadow: 0 4px 16px rgba(58, 141, 255, 0.15);
  border-color: rgba(58, 141, 255, 0.3);
}

.service-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #007bff;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.avatar-image:hover {
  transform: scale(1.05);
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: #28a745;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.service-info {
  flex: 1;
  min-width: 0;
}

.service-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.service-title i {
  color: #007bff;
  font-size: 18px;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #007bff;
  font-weight: 500;
}

.contact-item i {
  font-size: 16px;
}

.service-desc {
  font-size: 13px;
  color: #6c757d;
  margin-top: 4px;
}

.service-action {
  flex-shrink: 0;
}

.contact-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 80px;
  box-shadow: 0 2px 8px rgba(58, 141, 255, 0.3);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(58, 141, 255, 0.4);
  color: white;
  text-decoration: none;
}

.contact-btn i {
  font-size: 18px;
}

/* 现代化搜索框样式 */
.search-container {
  margin-bottom: 8px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 12px;
  padding: 4px;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.search-input-wrapper:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(58, 141, 255, 0.1);
}

.search-icon {
  padding: 0 12px;
  color: #6c757d;
  font-size: 16px;
}

.modern-search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 8px;
  font-size: 14px;
  background: transparent;
  color: #333;
}

.modern-search-input::placeholder {
  color: #6c757d;
}

.modern-search-btn {
  width: 100%;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(58, 141, 255, 0.3);
}

.modern-search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(58, 141, 255, 0.4);
}

.modern-search-btn:active {
  transform: translateY(0);
}

/* 查询结果样式 */
.query-results {
  margin-top: 8px;
}

.order-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #dee2e6;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.order-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: rgba(58, 141, 255, 0.3);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.order-id {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
}

.order-id i {
  color: #007bff;
}

.order-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.status-completed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-processing {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-pending {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.status-failed {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.order-content {
  padding: 16px;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
  margin: 0;
}

.info-item span {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.amount {
  color: #007bff !important;
  font-weight: 600 !important;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.empty-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.empty-text h4 {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 18px;
}

.empty-text p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* 表格样式 */
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 12px;
  border-collapse: collapse;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 6px 8px;
  line-height: 1.4;
  vertical-align: middle;
  border-top: 1px solid #dee2e6;
  font-size: 14px;
  height: 36px;
}

.table > thead > tr > th {
  background: #667eea;
  color: white;
  font-weight: 500;
  text-align: center;
  border: none;
  height: 40px;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f8f9fa;
}

.table-striped > tbody > tr:nth-of-type(even) {
  background-color: #ffffff;
}

.table-striped > tbody > tr:hover {
  background-color: #e9ecef;
}

.table-bordered {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #dee2e6;
}

.table-responsive {
  border-radius: 4px;
  overflow: hidden;
}

/* 表格定价样式 */
.table-pricing {
  background-color: #ffffff;
  text-align: center;
  border: 2px solid #ffffff;
  transition: all .15s ease-out;
}

.table-pricing:hover {
  border-color: #5ccdde;
  box-shadow: 0 0 20px rgba(0, 0, 0, .2);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* 颜色类 */
.text-primary {
  color: #5ccdde;
}

.text-success {
  color: #afde5c;
}

.text-info {
  color: #5cafde;
}

.text-warning {
  color: #deb25c;
}

.text-danger {
  color: #de815c;
}

.text-muted {
  color: #999999;
}

/* 图片样式 */
.img-circle {
  border-radius: 50%;
}

.img-thumbnail {
  display: inline-block;
  max-width: 100%;
  height: auto;
  padding: 4px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all .2s ease-in-out;
}

/* 列表组 */
.list-group {
  padding-left: 0;
  margin-bottom: 20px;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}

.list-group-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

/* 按钮组 */
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}

.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  display: table-cell;
  float: none;
  width: 1%;
}

.btn-group-justified > .btn-group .btn {
  width: 100%;
}

/* 警告框 */
.alert {
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 14px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.alert-success {
  color: #155724;
  background: #d4edda;
  border-color: #c3e6cb;
}

.alert-info {
  color: #0c5460;
  background: #d1ecf1;
  border-color: #bee5eb;
}

.alert-warning {
  color: #856404;
  background: #fff3cd;
  border-color: #ffeaa7;
}

.alert-danger {
  color: #721c24;
  background: #f8d7da;
  border-color: #f5c6cb;
}

/* 关闭按钮 */
.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: .2;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
  opacity: .5;
}

/* 字体图标 */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

/* 特定组件样式 */
.themed-background-flat {
  background-color: #f9f9f9;
}

.widget-icon {
  display: inline-block;
  width: 64px;
  height: 64px;
  line-height: 60px;
  margin: 5px;
  font-size: 28px;
  text-align: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, .05);
}

.widget-heading {
  margin: 10px 0;
}

.themed-background-info {
  background-color: #5bc0de;
}

/* 按钮效果 */
.btn-effect-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 搜索图标样式 */
.search-icon-addon {
  background: transparent !important;
  border: 1px solid transparent !important;
  border-left: none !important;
  padding: 0 12px !important;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-icon-addon:hover {
  background: rgba(102, 126, 234, 0.1) !important;
}

/* 下单页面搜索图标样式 */
.order-search-icon {
  color: #9ca3af !important;
  font-size: 18px;
  transition: color 0.3s ease;
  width: 18px;
  height: 18px;
  fill: currentColor;
  /* SVG渲染优化 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: smooth;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 下单页面 SVG 图标路径样式 */
.order-search-icon path {
  fill: currentColor;
}

.search-icon-addon:hover .order-search-icon {
  color: #007bff !important;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .main-container {
    padding: 5px 0;
  }

  .col-xs-12 {
    width: 100%;
    padding: 0 8px;
  }

  .modern-btn-group {
    gap: 4px;
  }

  .btn-modern {
    padding: 4px;
    font-size: 13px;
    height: 40px;
  }

  .modern-nav-tabs .nav-link {
    padding: 6px 4px;
    font-size: 13px;
    height: 40px;
  }

  .modern-nav-tabs .nav-link i {
    font-size: 14px;
  }

  .tab-content {
    padding: 6px;
  }

  .modal-dialog {
    margin: 8px;
    width: calc(100% - 16px);
  }

  .modal-body {
    max-height: 40vh;
  }

  .logo-header {
    padding: 8px;
  }

  .logo-avatar {
    width: 60px;
    height: 60px;
  }

  .avatar-img {
    border-radius: 30px;
  }

  .brand-subtitle {
    font-size: 12px;
  }

  .form-control {
    height: 28px;
    padding: 4px 8px;
    font-size: 12px;
  }

  .input-group-addon {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.5;
  }

  .btn {
    height: 28px;
    padding: 4px 12px;
  }

  .table > thead > tr > th,
  .table > tbody > tr > td {
    height: 32px;
    padding: 4px 6px;
  }

  .customer-service-card {
    padding: 12px;
    gap: 12px;
  }

  .avatar-image {
    width: 50px;
    height: 50px;
  }

  .online-status {
    width: 14px;
    height: 14px;
  }

  .service-title {
    font-size: 15px;
  }

  .contact-item {
    font-size: 13px;
  }

  .service-desc {
    font-size: 12px;
  }

  .contact-btn {
    padding: 10px 12px;
    font-size: 12px;
    min-width: 70px;
  }

  .contact-btn i {
    font-size: 16px;
  }

  .search-input-wrapper {
    border-radius: 8px;
    padding: 2px;
  }

  .modern-search-input {
    padding: 10px 6px;
    font-size: 13px;
  }

  .search-icon {
    padding: 0 8px;
    font-size: 14px;
  }

  .modern-search-btn {
    padding: 10px 12px;
    font-size: 13px;
  }

  .order-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    padding: 12px;
  }

  .order-status {
    align-self: flex-end;
  }

  .order-content {
    padding: 12px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .empty-state {
    padding: 40px 16px;
  }

  .empty-icon {
    font-size: 36px;
  }

  .empty-text h4 {
    font-size: 16px;
  }

  .empty-text p {
    font-size: 13px;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
    padding: 10px;
  }

  .stats-item {
    height: 75px;
    padding: 14px 6px;
    gap: 5px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(58, 141, 255, 0.12);
  }

  .stats-item:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 6px 18px rgba(58, 141, 255, 0.2);
  }

  .stats-number {
    font-size: 18px;
    font-weight: 700;
  }

  .stats-unit {
    font-size: 11px;
    font-weight: 600;
  }

  .stats-label {
    font-size: 11px;
    font-weight: 500;
  }

  .stats-header {
    padding: 8px 12px;
    height: 36px;
  }

  .stats-header .panel-title {
    font-size: 15px;
    font-weight: 600;
  }

  .stats-header i {
    font-size: 15px;
  }
}

@media (min-width: 768px) {
  .col-sm-6 {
    width: 50%;
    float: left;
  }

  .col-sm-10 {
    width: 83.33333333%;
  }

  .modern-nav-tabs .nav-link {
    padding: 20px 15px;
  }
}

@media (min-width: 992px) {
  .col-md-8 {
    width: 66.66666667%;
  }

  .tab-content {
    padding: 12px 16px;
  }
}

@media (min-width: 1200px) {
  .col-lg-5 {
    width: 41.66666667%;
  }

  .main-container {
    padding: 30px 0;
  }
}

/* 基础样式 */
* {
  box-sizing: border-box;
}

*:focus {
  outline: 2px solid #007bff;
  outline-offset: 1px;
}

.simple-notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1070;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.simple-notification-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

.simple-notification-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.simple-notification-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.simple-notification-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
  font-size: 16px;
  color: #495057;
  font-weight: 500;
}

.simple-notification-footer {
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  text-align: right;
  background-color: #f8f9fa;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .simple-notification-body {
    padding: 25px 15px 15px;
  }

  .notification-text {
    font-size: 14px;
  }
}

/* 商品信息展示区的新样式 */
.product-info-grid {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1.5fr;
  gap: 12px;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  align-items: center;
}
.info-item {
  display: flex;
  align-items: baseline;
  justify-content: center;
}
.info-item-label {
  font-size: 14px;
  color: #6c757d;
  margin-right: 8px;
}
.info-item-value {
  font-size: 1.15rem;
  font-weight: 700;
  color: #333;
}
.info-item-value.price {
  color: #dc3545;
  font-size: 1.45rem;
}

/* 商品说明模态框 */
.info-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1060;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.info-modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

.info-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.info-modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
}

.info-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  text-align: right;
  background-color: #f8f9fa;
}

/* 库存颜色 */
.stock-low {
  color: #dc3545 !important; /* 红色 */
}
.stock-medium {
  color: #ffc107 !important; /* 橙色 */
}
.stock-high {
  color: #28a745 !important; /* 绿色 */
}

/* 商品说明链接 */
.info-link {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
}

/* 优惠券区域样式 */
.coupon-area {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: flex-start; /* 左对齐 */
}

.coupon-checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0; /* 防止被压缩 */
}

.custom-checkbox {
  display: none; /* 隐藏原生checkbox */
}

.custom-checkbox + label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  user-select: none;
  color: #454e59;
  font-weight: normal;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  height: 36px; /* 与输入框高度保持一致 */
}

.custom-checkbox + label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid #adb5bd;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.2s ease;
}

.custom-checkbox + label::after {
  content: '';
  position: absolute;
  left: 7px;
  top: 50%;
  /* 调整勾号的位置和大小 */
  transform: translate(0, -60%) rotate(45deg) scale(0);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transition: all 0.2s ease;
}

.custom-checkbox:checked + label::before {
  background-color: #007bff;
  border-color: #007bff;
}

.custom-checkbox:checked + label::after {
  transform: translate(0, -60%) rotate(45deg) scale(1);
}

.coupon-input-container {
  flex-grow: 1;
  transition: all 0.3s ease;
}

/* 确认订单模态框样式 */
.order-summary {
  margin-bottom: 20px;
}
.order-summary p {
  margin: 5px 0;
  font-size: 16px;
}
.coupon-info {
  color: #28a745;
}
.final-price {
  font-size: 20px !important;
  font-weight: bold;
  color: #dc3545;
}

.payment-selection h5 {
  font-weight: bold;
  margin-bottom: 15px;
}
.payment-layout {
  display: flex;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  min-height: 200px;
}
.payment-channels {
  width: 120px;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
}
.payment-channels ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.payment-channels li {
  padding: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}
.payment-channels li:hover {
  background-color: #e9ecef;
}
.payment-channels li.active {
  background-color: #007bff;
  color: white;
  font-weight: bold;
}
.payment-ways {
  flex-grow: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.payment-way-card {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}
.payment-way-card:hover {
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0,123,255,0.2);
}
.payment-way-card.active {
  border-color: #007bff;
  background-color: #f0f8ff;
  font-weight: bold;
}
.payment-way-card .payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  font-size: 24px; /* 匹配原图大小 */
  line-height: 1;
  text-align: center;
}

.payment-way-card .fa-alipay {
  color: #00A0E9;
}

.payment-way-card .fa-weixin {
  color: #2DC100;
}

.payment-way-card .fa-qq {
  color: #12B7F5;
}

.payment-way-card .fa-money {
  color: #F67C0A;
}
.payment-way-card small {
  margin-left: auto;
  color: #6c757d;
}
.payment-placeholder {
  text-align: center;
  color: #6c757d;
  margin: auto;
}

/* 新增：确认订单模态框美化样式 */
.order-confirm-modal-content {
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  max-width: 550px;
  width: 95%;
  animation: slideIn 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.order-confirm-modal-header {
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.order-confirm-modal-header .info-modal-title {
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.order-confirm-modal-header .close {
  color: white;
  opacity: 0.8;
  text-shadow: none;
  font-size: 28px;
}
.order-confirm-modal-header .close:hover {
  opacity: 1;
}

.order-confirm-modal-body {
  padding: 25px;
  background-color: #ffffff;
}

.order-summary-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  border: 1px solid #e9ecef;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 15px;
}

.summary-item strong {
  color: #495057;
  font-weight: 600;
}

.summary-item span {
  color: #6c757d;
}

.coupon-applied-badge {
  color: #28a745;
  font-weight: 600;
}

.summary-divider {
  border-top: 1px dashed #ced4da;
  margin: 15px 0;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
}

.summary-total strong {
  font-size: 18px;
  color: #343a40;
}

.summary-total .final-price-value {
  font-size: 24px;
  font-weight: 700;
  color: #dc3545;
}

.payment-selection-area h5 {
  font-size: 18px;
  font-weight: 700;
  color: #343a40;
  margin-bottom: 20px;
  text-align: center;
}

.payment-layout-vertical {
  display: flex;
  gap: 20px;
}

.payment-tabs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex-shrink: 0;
}

.payment-tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 12px;
  cursor: pointer;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  flex-direction: column;
  gap: 5px;
  color: #6c757d;
}

.payment-tab-item:hover {
  border-color: #3a8dff;
  background: #f1f3f5;
}

.payment-tab-item.active {
  border-color: #3a8dff;
  background: #e7f3ff;
  color: #3a8dff;
  box-shadow: 0 4px 10px rgba(58, 141, 255, 0.2);
}

.payment-tab-item .payment-icon {
  font-size: 28px;
  line-height: 1;
}

.payment-tab-item span {
  font-size: 12px;
  font-weight: 600;
}

.payment-options {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.payment-option-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 70px;
}

.payment-option-card:hover {
  border-color: #3a8dff;
  background: #f1f3f5;
}

.payment-option-card.selected {
  border-color: #3a8dff;
  background: #e7f3ff;
}

.payment-option-card.selected::after {
  content: '✔';
  position: absolute;
  top: -10px;
  right: -10px;
  width: 24px;
  height: 24px;
  background: #3a8dff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.payment-option-card .payment-icon {
  font-size: 28px;
  margin-right: 15px;
}

.payment-option-card span {
  font-weight: 600;
  color: #343a40;
}

.payment-option-card small {
  margin-left: auto;
  color: #6c757d;
  font-weight: 500;
}

.order-confirm-modal-footer {
  padding: 20px 25px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  border-radius: 0 0 12px 12px;
}

.btn-cancel-order, .btn-confirm-order {
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  transition: all 0.3s ease;
}

.btn-cancel-order {
  background-color: #e9ecef;
  color: #495057;
}
.btn-cancel-order:hover {
  background-color: #ced4da;
}

.btn-confirm-order {
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(58, 141, 255, 0.3);
}
.btn-confirm-order:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(58, 141, 255, 0.4);
}
.btn-confirm-order:disabled {
  background: #ced4da;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* 新增：支付二维码模态框美化样式 */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 1080;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.payment-modal-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 380px;
  width: 92%;
  animation: slideInUp 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-align: center;
  overflow: hidden;
}

.payment-modal-header {
  padding: 20px 25px;
  background: linear-gradient(135deg, #4a90e2 0%, #0052d4 100%);
  color: white;
  position: relative;
}

.payment-modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.15);
}

.payment-modal-close {
  position: absolute;
  top: 12px;
  right: 15px;
  background: none;
  border: none;
  color: white;
  opacity: 0.8;
  font-size: 26px;
  cursor: pointer;
  transition: opacity 0.2s;
}
.payment-modal-close:hover {
  opacity: 1;
}

.payment-modal-body {
  padding: 25px 30px;
}

.payment-info-group {
  margin-bottom: 20px;
}

.payment-method {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 12px;
}

.payment-icon {
  font-size: 26px;
}
.payment-icon.alipay {
  color: #1677FF;
  width: 26px;
  height: 26px;
}

.payment-name {
  font-size: 17px;
  font-weight: 500;
  color: #343a40;
}

.payment-price {
  font-size: 40px;
  font-weight: 700;
  color: #d9534f;
  line-height: 1;
}

.currency-symbol {
  font-size: 24px;
  vertical-align: middle;
  margin-right: 2px;
}

.qrcode-wrapper {
  width: 210px;
  height: 210px;
  margin: 0 auto 20px;
  position: relative;
}

.qrcode-box {
  width: 100%;
  height: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 10px;
}

.qrcode-box img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

.qrcode-loading-spinner {
  font-size: 40px;
  color: #adb5bd;
}

.qrcode-error-tip {
  color: #d9534f;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}
.qrcode-error-tip i {
  font-size: 32px;
}

.payment-footer {
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-timer {
  font-size: 15px;
  color: #6c757d;
}

.time-highlight {
  font-weight: 600;
  color: #3a8dff;
  font-size: 16px;
}

.auto-close-tip {
  font-size: 15px;
  color: #28a745;
}

/* 支付成功动画 */
.payment-success-animation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #4caf50;
  stroke-miterlimit: 10;
  box-shadow: inset 0px 0px 0px #4caf50;
  animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
}
.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #4caf50;
  fill: none;
  animation: stroke .6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.checkmark-check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  stroke-width: 3;
  fill: none;
  stroke: #ffffff;
  animation: stroke .3s cubic-bezier(0.65, 0, 0.45, 1) .8s forwards;
}
.success-text {
  margin-top: 15px;
  font-size: 20px;
  font-weight: 600;
  color: #4caf50;
}

/* 支付状态展示 */
.payment-status-display {
  padding: 40px 0;
}
.status-icon {
  font-size: 64px;
  margin-bottom: 20px;
}
.status-icon.error {
  color: #d9534f;
}
.status-icon.cancelled {
  color: #adb5bd;
}
.status-text {
  font-size: 20px;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 25px;
}
.btn-re-order {
  background: linear-gradient(135deg, #3a8dff 0%, #0052d4 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}
.btn-re-order:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(58, 141, 255, 0.3);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes scale {
  0%, 100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 40px #4caf50;
  }
}
</style>
