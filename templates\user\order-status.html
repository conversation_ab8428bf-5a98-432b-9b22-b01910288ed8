<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title|default:"订单详情" }}</title>
    <!-- 原来的字体和图标链接 -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- 使用阿里云CDN的Font Awesome链接 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加备用CDN，确保图标能够加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 保留中文字体 -->
    <link href="https://fonts.loli.net/css2?family=Montserrat:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/css/user/order-status_card.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <a href="/" class="logo">
            <i class="fa-solid fa-store"></i>
            无名SUP
        </a>
    </nav>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <h1 class="page-title">{{ page_title|default:"订单详情" }}</h1>
        
        <!-- 订单状态卡片 -->
        <div class="order-card">
            <div class="order-header">
                <div class="order-id">订单号: {{ order_id|default:"未知订单" }}</div>
                <div class="order-date"><span class="material-icons">event</span> {{ create_time|default:"未知时间" }}</div>
                <div class="order-status {% if status_raw == 'completed' %}completed{% elif status_raw == 'failed' %}failed{% elif status_raw == 'pending' %}pending{% endif %}">
                    <span class="material-icons">
                        {% if status_raw == 'completed' %}check_circle{% elif status_raw == 'failed' %}error{% elif status_raw == 'pending' %}schedule{% else %}help{% endif %}
                    </span> 
                    {{ status|default:"未知状态" }}
                </div>
            </div>
            
            <!-- 订单状态进度条 -->
            <div class="order-progress">
                <div class="progress-title">
                    <span class="material-icons">check_circle</span>
                    订单进度
                </div>
                
                <div class="progress-steps">
                    {% for step in order_history %}
                    <div class="progress-step {% if forloop.counter <= order_history|length %}completed{% endif %}">
                        <div class="step-icon">
                            <span class="material-icons">
                                {% if step.action == 'order_created' %}add_shopping_cart
                                {% elif step.action == 'payment_success' %}payment
                                {% elif step.action == 'order_processing' %}hourglass_empty
                                {% elif step.action == 'order_completed' %}check_circle
                                {% elif step.action == 'order_failed' %}error
                                {% else %}help
                                {% endif %}
                            </span>
                        </div>
                        <div class="step-label">{{ step.description|default:"处理中" }}</div>
                        <div class="step-time">{{ step.timestamp|slice:":19"|slice:"11:" }}</div>
                    </div>
                    {% empty %}
                    <div class="progress-step completed">
                        <div class="step-icon">
                            <span class="material-icons">add_shopping_cart</span>
                        </div>
                        <div class="step-label">订单创建</div>
                        <div class="step-time">{{ create_time|slice:":19"|slice:"11:" }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="order-item-info">
                <div class="order-item-row">
                    <div class="order-item-label">购买商品:</div>
                    <div class="order-item-value">{{ product_name|default:"未知商品" }}</div>
                </div>
                <div class="order-item-row">
                    <div class="order-item-label">商品ID:</div>
                    <div class="order-item-value product-id">{{ product_id|default:"未知ID" }}</div>
                </div>
                <div class="order-item-row">
                    <div class="order-item-label">订单金额:</div>
                    <div class="order-item-value"><span class="order-price">¥{{ final_price|default:"0.00" }}</span></div>
                </div>
                <div class="order-item-row">
                    <div class="order-item-label">支付方式:</div>
                    <div class="order-item-value">{{ payment_method|default:"未知支付方式" }}</div>
                </div>
            </div>
        </div>
        
        <!-- 卡密信息卡片 -->
        {% if fulfillment_type == 2 %}
        <div class="cdkey-card">
            <h3 class="cdkey-title">
                <span class="material-icons">vpn_key</span>
                卡密信息
            </h3>
            
            <div class="cdkey-container">
                <div class="anti-screenshot"></div>
                
                <div class="cdkey-warning">
                    <span class="material-icons">info</span>
                    为保障卡密安全，卡密默认处于隐藏状态，点击"显示卡密"按钮查看
                </div>
                
                <div class="cdkey-value" id="cdkey">{{ card_content|default:"暂无卡密信息" }}</div>
                
                <div class="cdkey-actions">
                    <button class="cdkey-btn reveal-btn" id="reveal-btn">
                        <span class="material-icons">visibility</span>
                        显示卡密
                    </button>
                    
                    <button class="cdkey-btn copy-btn" id="copy-btn">
                        <span class="material-icons">content_copy</span>
                        复制卡密
                    </button>
                </div>
            </div>
            
            <div class="cdkey-note">
                <strong>温馨提示:</strong> 为保障您的账号安全，请勿将卡密泄露给他人，本平台不会以任何理由向您索要卡密信息。卡密显示后将在<span id="countdown">30</span>秒后自动隐藏。
            </div>
        </div>
        {% endif %}
        
        <!-- 商品说明卡片 -->
        <div class="instructions-card">
            <h3 class="instructions-title">
                <i class="fa-solid fa-info-circle"></i>
                商品说明
            </h3>
            
            <div class="product-description">
                {% if product_info %}
                    <div class="description-content">
                        {{ product_info|safe }}
                    </div>
                {% else %}
                    <div class="no-description">
                        <i class="fa-solid fa-exclamation-triangle"></i>
                        <p>暂无商品说明信息</p>
                        <p class="description-hint">商家未提供详细的商品说明，如有疑问请联系客服。</p>
                    </div>
                {% endif %}
            </div>
        </div>

    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="logo">
                <i class="fa-solid fa-store"></i>
                无名SUP
            </div>
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>

    
    
    <script src="/static/js/user/order-status_card.js"></script>
</body>
</html> 