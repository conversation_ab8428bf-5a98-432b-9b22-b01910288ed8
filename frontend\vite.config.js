import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import fs from 'fs'

// 扫描Vue文件的辅助函数
function scanVueFiles(dir) {
  const entries = {}

  function scanDirectory(currentDir, relativePath = '') {
    if (!fs.existsSync(currentDir)) return

    const files = fs.readdirSync(currentDir)

    files.forEach(file => {
      const fullPath = path.join(currentDir, file)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        scanDirectory(fullPath, path.join(relativePath, file))
      } else if (file.endsWith('.vue')) {
        const name = path.join(relativePath, file.replace('.vue', ''))
        // 统一使用正斜杠，确保跨平台兼容性
        const key = name.replace(/\\/g, '/')
        // 确保fullPath也使用正确的分隔符
        const normalizedFullPath = fullPath.replace(/\\/g, '/')
        entries[key] = normalizedFullPath
      }
    })
  }

  scanDirectory(dir)
  return entries
}

// Vue入口生成器插件
function vueEntryGeneratorPlugin() {
  return {
    name: 'vue-entry-generator',
    resolveId(id) {
      // 处理虚拟入口文件
      if (id.startsWith('virtual:vue-entry:')) {
        return id
      }
    },
    load(id) {
      // 为虚拟入口文件生成内容
      if (id.startsWith('virtual:vue-entry:')) {
        const vueFilePath = id.replace('virtual:vue-entry:', '')
        // 构建绝对路径，但使用正确的路径分隔符
        const absolutePath = path.resolve(__dirname, 'src', vueFilePath + '.vue').replace(/\\/g, '/')

        // 生成入口文件内容
        return `
import { createApp } from 'vue'
import Component from '${absolutePath}'

// 创建Vue应用实例
const app = createApp(Component)

// 注入Django上下文数据
if (window.__DJANGO_CONTEXT__) {
  app.provide('djangoContext', window.__DJANGO_CONTEXT__)

  // 将Django数据混入到组件的data中
  const originalData = Component.data || (() => ({}))
  Component.data = function() {
    const data = originalData.call(this)
    return {
      ...data,
      djangoContext: window.__DJANGO_CONTEXT__
    }
  }
}

// 等待DOM加载完成后挂载
document.addEventListener('DOMContentLoaded', () => {
  const mountPoint = document.getElementById('app')
  if (mountPoint) {
    app.mount('#app')
    console.log('[Vue] 应用已成功挂载')
  } else {
    console.error('[Vue] 未找到挂载点 #app')
  }
})

// 导出应用实例供调试使用
window.__VUE_APP__ = app
`
      }
    }
  }
}

// 生成虚拟入口映射
function generateVueEntries(dir) {
  const entries = {}
  const vueFiles = scanVueFiles(dir)

  Object.keys(vueFiles).forEach(key => {
    // 为每个Vue文件创建虚拟入口
    entries[key] = `virtual:vue-entry:${key}`
  })

  return entries
}

export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 使用自定义分隔符避免与Django模板语法冲突
          delimiters: ['[[', ']]']
        }
      }
    }),
    // 添加Vue入口生成器插件
    vueEntryGeneratorPlugin()
  ],

  root: path.resolve(__dirname, 'src'),

  build: {
    // 构建输出到Django静态文件目录（生产环境）
    outDir: path.resolve(__dirname, '../static/vue'),
    emptyOutDir: false, // 不清空目录，保持现有文件

    rollupOptions: {
      input: {
        // 使用虚拟入口文件系统
        ...generateVueEntries(path.resolve(__dirname, 'src'))
      },
      output: {
        // 保持目录结构
        entryFileNames: '[name].js',
        chunkFileNames: 'chunks/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',

        // 确保输出文件保持相对路径结构
        manualChunks: undefined
      }
    },

    // 生成manifest文件用于Django引用
    manifest: true,

    // 生产环境压缩
    minify: 'terser',
    sourcemap: false,

    // 确保Vue组件能正确构建
    lib: false,

    // 目标环境
    target: 'es2015',

    // 确保外部依赖正确处理
    commonjsOptions: {
      include: [/js-md5/, /node_modules/]
    }
  },

  server: {
    port: 5173,
    host: 'localhost',

    // 代理Django服务器
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/static': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/media': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/admin': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    },

    // 热更新配置
    hmr: {
      port: 5174,
      host: 'localhost'
    },

    // 开发服务器配置
    cors: true,
    open: false // 不自动打开浏览器
  },

  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'vue': 'vue/dist/vue.esm-bundler.js'
    }
  },

  css: {
    preprocessorOptions: {
      scss: {
        // 移除全局样式变量导入，每个组件独立管理样式
      }
    }
  },

  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
})
