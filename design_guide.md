# Art Design Pro 技术文档

本文档旨在总结 Art Design Pro 项目的 UI 风格、配色方案和技术实现，为后续的二次开发提供参考。

## 1. 技术栈

项目基于以下技术构建：

- **Vue 3**: 渐进式 JavaScript 框架，使用 Composition API
- **Vite**: 新一代前端构建工具，提供快速的开发体验
- **Element Plus**: 基于 Vue 3 的组件库，提供丰富的 UI 组件
- **Sass**: CSS 预处理器，用于编写更强大、更易于维护的样式
- **Pinia**: Vue 的状态管理库，用于管理应用状态
- **TypeScript**: 提供类型安全和更好的开发体验

### 1.1. 项目结构

```
src/
├── assets/
│   ├── styles/          # 样式文件
│   │   ├── variables.scss    # CSS 变量定义
│   │   ├── app.scss         # 全局样式
│   │   ├── el-ui.scss       # Element Plus 样式优化
│   │   ├── theme-animation.scss  # 主题切换动画
│   │   └── ...
│   ├── fonts/           # 字体文件
│   └── icons/           # 图标资源
├── components/          # 公共组件
├── composables/         # 组合式函数
├── store/              # 状态管理
├── utils/              # 工具函数
└── views/              # 页面组件
```

### 1.2. 样式文件加载顺序

项目在 `main.ts` 中按以下顺序加载样式文件：

1. `reset.scss` - 重置 HTML 样式
2. `app.scss` - 全局样式
3. `el-ui.scss` - Element Plus 样式优化
4. `mobile.scss` - 移动端样式优化
5. `change.scss` - 主题切换过渡优化
6. `theme-animation.scss` - 主题切换动画
7. `el-light.scss` - Element Plus 亮色主题
8. `el-dark.scss` - Element Plus 暗色主题
9. `dark.scss` - 系统暗色主题

## 2. 设计系统

项目的视觉设计系统由以下核心元素构成：

### 2.1. 颜色系统

项目定义了一套完整的颜色系统，并支持亮色（Light）和暗色（Dark）两种主题模式。颜色变量通过 CSS 自定义属性（CSS Variables）在 `:root` 和 `html.dark` 选择器中定义，实现了动态主题切换。

#### 2.1.1. 主题色

| 类别 | 变量名 | 亮色模式 (RGB) | 暗色模式 (RGB) |
| :--- | :--- | :--- | :--- |
| 主要 | `--art-primary` | `93, 135, 255` | `93, 135, 255` |
| 次要 | `--art-secondary` | `73, 190, 255` | `73, 190, 255` |
| 成功 | `--art-success` | `19, 222, 185` | `19, 222, 185` |
| 警告 | `--art-warning` | `255, 174, 31` | `255, 174, 31` |
| 错误 | `--art-error` | `250, 137, 107` | `250, 137, 107` |
| 信息 | `--art-info` | `107, 125, 155` | `107, 125, 155` |
| 危险 | `--art-danger` | `255, 77, 79` | `255, 77, 79` |

#### 2.1.2. 背景色

背景色同样分为亮色和暗色两种模式，为不同状态提供了视觉反馈。

- **亮色模式**:
  - `--art-bg-color`: `#fafbfc` (最底层背景)
  - `--art-main-bg-color`: `#ffffff` (主内容区背景)
  - `--art-bg-primary`: `236, 242, 255`
- **暗色模式**:
  - `--art-bg-color`: `#070707`
  - `--art-main-bg-color`: `#161618`
  - `--art-bg-primary`: `37, 54, 98`

#### 2.1.3. 灰色系

灰色系用于文本、边框和背景，提供了丰富的层次感。

| 变量名 | 亮色模式 | 暗色模式 |
| :--- | :--- | :--- |
| `--art-gray-100` | `#f9f9f9` | `#1b1c22` |
| `--art-gray-500` | `#99a1b7` | `#636674` |
| `--art-gray-900` | `#071437` | `#f5f5f5` |

### 2.2. 字体

项目引入了两种自定义字体：

- **DMSans**: 用于常规文本，风格现代、易读。
- **Montserrat**: 用于标题或特殊文本，具有几何美感。

### 2.3. 布局和间距

- **响应式设计**: 使用媒体查询来适应不同设备尺寸，如笔记本、平板和手机。
- **卡片式布局**: 页面内容多采用卡片（`.page-content`, `.art-custom-card`）进行组织，使布局清晰、模块化。
- **Flexbox**: 广泛使用 Flexbox 进行元素对齐和分布。

### 2.4. 阴影和边框

项目定义了两种盒子模式，可以通过 `data-box-mode` 属性进行切换：

- **边框模式 (`border-mode`)**:
  - 卡片和侧边栏使用由 `--art-card-border` 变量定义的边框
  - 边框颜色：`rgba(var(--art-gray-300-rgb), 0.6)`
  - 适用于简洁、扁平化的设计风格

- **阴影模式 (`shadow-mode`)**:
  - 卡片使用 `box-shadow` 创造悬浮感，同时保留了较浅的边框
  - 阴影效果：`0px 0px 4px 0px rgba(0, 0, 0, 0.04)`
  - 边框颜色：`rgba(var(--art-gray-300-rgb), 0.3)`
  - 适用于现代化、立体感的设计风格

#### 2.4.1. 阴影层级系统

项目定义了多个阴影层级，为不同组件提供丰富的立体感：

- `--art-box-shadow-xs`: 最小阴影，用于微妙的层次区分
- `--art-box-shadow-sm`: 小阴影，用于按钮悬停等交互状态
- `--art-box-shadow`: 标准阴影，用于卡片等主要容器
- `--art-box-shadow-lg`: 大阴影，用于模态框、下拉菜单等浮层组件

### 2.5. 圆角系统

项目使用动态圆角系统，通过 `--custom-radius` CSS 变量控制：

- **基础圆角**: `calc(var(--custom-radius) / 3 + 2px)`
- **小圆角**: `calc(var(--custom-radius) / 3 + 4px)`
- **页面内容圆角**: `calc(var(--custom-radius) / 2 + 2px)`
- **卡片圆角**: `calc(var(--custom-radius) + 4px)`

这种设计确保了整个应用的圆角风格保持一致，同时允许用户自定义圆角大小。

### 2.6. 响应式断点

项目定义了完整的响应式断点系统：

```scss
// 设备尺寸断点
$device-notebook: 1600px;    // 笔记本电脑
$device-ipad-pro: 1180px;    // iPad Pro
$device-ipad: 800px;         // iPad
$device-ipad-vertical: 900px; // iPad 竖屏
$device-phone: 500px;        // 手机
```

每个断点都有对应的样式优化，确保在不同设备上都有良好的用户体验。

## 3. 组件样式详解

### 3.1. 按钮组件

#### 3.1.1. 基础按钮样式

- **图标按钮 (`.btn-icon`)**:
  - 字体大小：`10px`
  - 用于工具栏和操作按钮

- **红色按钮 (`.el-btn-red`)**:
  - 颜色：`#fa6962`
  - 悬停效果：`opacity: 0.9`
  - 点击效果：`opacity: 0.7`
  - 用于危险操作或重要提示

#### 3.1.2. 表格按钮 (`.btn-text`)

专为表格操作设计的按钮组件：

```scss
.btn-text {
  display: inline-block;
  min-width: 34px;
  height: 34px;
  padding: 0 10px;
  margin-right: 10px;
  font-size: 13px;
  line-height: 34px;
  color: #666;
  cursor: pointer;
  background-color: rgba(var(--art-gray-200-rgb), 0.7);
  border-radius: 6px;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: rgba(var(--art-gray-300-rgb), 0.5);
  }
}
```

#### 3.1.3. Element Plus 按钮优化

- **默认高度**: `36px` (通过 `--el-component-custom-height` 控制)
- **圆角**: 动态计算 `calc(var(--custom-radius) / 3 + 2px)`
- **水波纹效果**: 文字层级高于水波纹，确保视觉效果正确

### 3.2. 卡片组件

#### 3.2.1. 页面内容卡片 (`.page-content`)

```scss
.page-content {
  position: relative;
  box-sizing: border-box;
  padding: 20px;
  overflow: hidden;
  background: var(--art-main-bg-color);
  border-radius: calc(var(--custom-radius) / 2 + 2px);
}
```

#### 3.2.2. 表格卡片 (`.art-table-card`)

- 专为表格设计的卡片容器
- 具有圆角和 flex 布局
- 内容自适应高度
- 支持边框/阴影模式切换

#### 3.2.3. 自定义卡片 (`.art-custom-card`)

- 通用卡片样式
- 边框和阴影根据 `data-box-mode` 属性动态变化
- 背景色：`var(--art-main-bg-color)`
- 圆角：`calc(var(--custom-radius) + 4px)`

#### 3.2.4. 仪表板卡片样式

```scss
.card {
  box-sizing: border-box;
  padding: 20px;
  background-color: var(--art-main-bg-color);
  border-radius: var(--custom-radius);

  .card-header {
    padding-bottom: 15px;

    .title {
      font-size: 18px;
      font-weight: 500;
      color: var(--art-gray-900);
    }

    .subtitle {
      font-size: 14px;
      color: var(--art-gray-500);
    }
  }
}
```

### 3.3. 徽章组件

#### 3.3.1. 点状徽章 (`.art-badge`)

```scss
.art-badge {
  position: absolute;
  top: 0;
  right: 20px;
  bottom: 0;
  width: 6px;
  height: 6px;
  margin: auto;
  background: #ff3860;
  border-radius: 50%;
  animation: breathe 1.5s ease-in-out infinite;
}
```

**呼吸动画效果**:
```scss
@keyframes breathe {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}
```

#### 3.3.2. 文字徽章 (`.art-text-badge`)

```scss
.art-text-badge {
  position: absolute;
  top: 0;
  right: 12px;
  bottom: 0;
  min-width: 20px;
  height: 18px;
  line-height: 17px;
  padding: 0 5px;
  margin: auto;
  font-size: 10px;
  color: #fff;
  text-align: center;
  background: #fd4e4e;
  border-radius: 4px;
}
```

#### 3.3.3. 徽章位置变体

- `.art-badge-horizontal`: 水平菜单徽章 (`right: 0`)
- `.art-badge-mixed`: 混合菜单徽章 (`right: 0`)
- `.art-badge-dual`: 双列菜单徽章 (`right: 5px; top: 5px`)

### 3.4. 状态徽章

项目中还包含状态徽章，用于显示不同的业务状态：

```scss
.stock-badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
}

.in-stock {
  color: rgb(var(--art-success));
  background-color: rgba(var(--art-success-rgb), 0.1);
}

.medium-stock {
  color: rgb(var(--art-info));
  background-color: rgba(var(--art-info-rgb), 0.1);
}
```

## 4. 主题切换系统

### 4.1. 主题切换架构

主题切换系统基于以下核心技术：

- **CSS 变量**: 所有颜色相关的样式都使用 CSS 变量
- **类名切换**: 通过添加/移除 `.dark` 类实现主题切换
- **状态管理**: 使用 Pinia 管理主题状态
- **本地存储**: 主题偏好持久化存储

### 4.2. CSS 变量系统

#### 4.2.1. 变量定义结构

```scss
// variables.scss
:root {
  // 亮色主题变量
  --art-primary: 93, 135, 255;
  --art-bg-color: #fafbfc;
  --art-main-bg-color: #ffffff;
  // ... 其他变量
}

html.dark {
  // 暗色主题变量
  --art-bg-color: #070707;
  --art-main-bg-color: #161618;
  // ... 其他变量
}
```

#### 4.2.2. 变量使用方式

```scss
// RGB 值变量（用于透明度控制）
background-color: rgba(var(--art-primary), 0.1);

// 直接颜色变量
background-color: var(--art-bg-color);
```

### 4.3. 主题切换实现

#### 4.3.1. 状态管理 (useSettingStore)

```typescript
// store/modules/setting.ts
export const useSettingStore = defineStore('settingStore', () => {
  // 系统主题类型
  const systemThemeType = ref(SystemThemeEnum.AUTO)
  const systemThemeMode = ref(SystemThemeEnum.AUTO)

  // 判断是否为暗色模式
  const isDark = computed((): boolean => {
    return systemThemeType.value === SystemThemeEnum.DARK
  })

  // 设置全局主题
  const setGlopTheme = (theme: SystemThemeEnum, themeMode: SystemThemeEnum) => {
    systemThemeType.value = theme
    systemThemeMode.value = themeMode
  }
})
```

#### 4.3.2. 主题切换逻辑 (useTheme)

```typescript
// composables/useTheme.ts
export const useTheme = () => {
  const settingStore = useSettingStore()

  // 设置系统主题
  const setSystemTheme = (theme: SystemThemeEnum, themeMode?: SystemThemeEnum) => {
    // 临时禁用过渡效果
    disableTransitions()

    const el = document.getElementsByTagName('html')[0]
    const isDark = theme === SystemThemeEnum.DARK

    // 设置主题类名
    const currentTheme = AppConfig.systemThemeStyles[theme]
    if (currentTheme) {
      el.setAttribute('class', currentTheme.className)
    }

    // 动态计算 Element Plus 主题色
    const primary = settingStore.systemThemeColor
    for (let i = 1; i <= 9; i++) {
      document.documentElement.style.setProperty(
        `--el-color-primary-light-${i}`,
        isDark ? getDarkColor(primary, i / 10) : getLightColor(primary, i / 10)
      )
    }

    // 更新状态
    settingStore.setGlopTheme(theme, themeMode)

    // 恢复过渡效果
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        enableTransitions()
      })
    })
  }

  // 自动主题检测
  const setSystemAutoTheme = () => {
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setSystemTheme(SystemThemeEnum.DARK, SystemThemeEnum.AUTO)
    } else {
      setSystemTheme(SystemThemeEnum.LIGHT, SystemThemeEnum.AUTO)
    }
  }
}
```

### 4.4. 主题切换动画

#### 4.4.1. View Transition API

项目使用现代浏览器的 View Transition API 实现流畅的主题切换动画：

```typescript
// utils/theme/animation.ts
export const themeAnimation = (e: any) => {
  const x = e.clientX
  const y = e.clientY
  // 计算鼠标点击位置距离视窗的最大圆半径
  const endRadius = Math.hypot(Math.max(x, innerWidth - x), Math.max(y, innerHeight - y))

  // 设置CSS变量
  document.documentElement.style.setProperty('--x', x + 'px')
  document.documentElement.style.setProperty('--y', y + 'px')
  document.documentElement.style.setProperty('--r', endRadius + 'px')

  if (document.startViewTransition) {
    document.startViewTransition(() => toggleTheme())
  } else {
    toggleTheme()
  }
}
```

#### 4.4.2. 动画样式定义

```scss
// theme-animation.scss
@keyframes clip {
  from {
    clip-path: circle(0% at var(--x) var(--y));
  }
  to {
    clip-path: circle(var(--r) at var(--x) var(--y));
  }
}

html {
  &::view-transition-new(*) {
    animation: clip 0.5s ease-in;
  }

  &.dark {
    &::view-transition-old(*) {
      animation: clip 0.5s ease-in reverse;
    }
  }
}
```

### 4.5. 过渡效果优化

#### 4.5.1. 过渡禁用机制

```scss
// change.scss
.theme-change {
  * {
    transition: 0s !important;
  }

  .el-switch__core,
  .el-switch__action {
    transition: all 0.3s !important;
  }
}
```

这种机制确保主题切换时不会出现不协调的过渡效果，同时保留必要组件的动画。

### 4.6. Element Plus 主题集成

#### 4.6.1. 自定义主题文件

- **el-light.scss**: Element Plus 亮色主题定制
- **el-dark.scss**: Element Plus 暗色主题定制

#### 4.6.2. 动态主题色计算

```typescript
// utils/ui/colors.ts
export function setElementThemeColor(color: string): void {
  const mixColor = '#ffffff'
  const elStyle = document.documentElement.style

  // 动态计算主题色的各个层级
  for (let i = 1; i <= 9; i++) {
    elStyle.setProperty(
      `--el-color-primary-light-${i}`,
      getLightColor(color, i / 10)
    )
  }
}
```

这种设计确保了 Element Plus 组件与自定义主题的完美融合。

## 5. 布局系统

### 5.1. 容器布局

#### 5.1.1. 主布局容器

```scss
.layouts {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  background: var(--art-bg-color);
  transition: padding 0.3s ease-in-out;

  .layout-content {
    box-sizing: border-box;
    width: calc(100% - 40px);
    margin: auto;
  }
}
```

#### 5.1.2. 容器宽度模式

项目支持多种容器宽度模式：

- **FULL**: 全宽模式，充分利用屏幕空间
- **FIXED**: 固定宽度模式，适合内容展示
- **FLUID**: 流式布局，自适应内容

### 5.2. 菜单布局

#### 5.2.1. 菜单类型

- **LEFT**: 左侧菜单布局
- **TOP**: 顶部菜单布局
- **MIXED**: 混合菜单布局
- **DUAL**: 双列菜单布局

#### 5.2.2. 菜单主题

```typescript
// 菜单主题配置
const themeList = [
  {
    theme: MenuThemeEnum.DESIGN,
    background: '#FFFFFF',
    systemNameColor: 'var(--art-text-gray-800)',
    iconColor: '#6B6B6B',
    textColor: '#29343D',
    textActiveColor: '#3F8CFF',
    iconActiveColor: '#333333',
    // ...
  },
  {
    theme: MenuThemeEnum.DARK,
    background: '#191A23',
    systemNameColor: '#BABBBD',
    iconColor: '#BABBBD',
    textColor: '#BABBBD',
    textActiveColor: '#FFFFFF',
    iconActiveColor: '#FFFFFF',
    // ...
  }
]
```

### 5.3. 响应式布局

#### 5.3.1. 移动端适配

```scss
@media only screen and (max-width: $device-ipad) {
  .layouts {
    width: 100%;
    min-height: 100vh;
    padding-left: 0 !important;
    overflow-y: scroll;

    .layout-content {
      width: calc(100% - 20px);
    }
  }
}
```

#### 5.3.2. 移动端组件优化

```scss
@media screen and (max-width: $device-phone) {
  * {
    cursor: default !important;
  }

  .el-message-box,
  .el-message,
  .el-dialog {
    width: calc(100% - 24px) !important;
  }
}
```

## 6. 动画与交互

### 6.1. 过渡动画

#### 6.1.1. 页面过渡

项目支持多种页面过渡效果：

- `slide-left`: 左滑效果
- `slide-right`: 右滑效果
- `fade`: 淡入淡出效果
- `zoom`: 缩放效果

#### 6.1.2. 组件动画

```scss
// 按钮悬停动画
.el-button {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

// 卡片悬停动画
.art-custom-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
}
```

### 6.2. 加载动画

#### 6.2.1. 进度条

```scss
#nprogress .bar {
  z-index: 2400;
  background-color: color-mix(in srgb, var(--main-color) 65%, white);
}
```

#### 6.2.2. 骨架屏

项目使用 Element Plus 的骨架屏组件，提供优雅的加载体验。

### 6.3. 微交互

#### 6.3.1. 水波纹效果

```scss
.el-button > span {
  position: relative;
  z-index: 10;
}
```

确保水波纹效果在文字下方，不影响文字显示。

#### 6.3.2. 呼吸动画

用于徽章组件的呼吸动画，吸引用户注意：

```scss
@keyframes breathe {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}
```

## 7. 开发指南

### 7.1. 样式开发规范

#### 7.1.1. CSS 变量使用

- 优先使用 CSS 变量而非硬编码颜色值
- 使用语义化的变量名
- 支持主题切换的样式必须使用 CSS 变量

```scss
// ✅ 推荐
background-color: var(--art-main-bg-color);
color: var(--art-text-gray-900);

// ❌ 不推荐
background-color: #ffffff;
color: #333333;
```

#### 7.1.2. 响应式开发

- 使用项目定义的断点变量
- 移动端优先的设计思路
- 确保在所有设备上的可用性

```scss
// 使用项目断点
@media screen and (max-width: $device-phone) {
  .component {
    padding: 10px;
  }
}
```

#### 7.1.3. 组件样式隔离

- 使用 `scoped` 样式避免样式污染
- 深度选择器使用 `:deep()` 语法
- 组件样式文件与组件文件同目录

```vue
<style scoped lang="scss">
.component {
  // 组件样式

  :deep(.el-button) {
    // 深度选择器修改子组件样式
  }
}
</style>
```

### 7.2. 主题定制

#### 7.2.1. 添加新的主题色

1. 在 `variables.scss` 中定义新的 CSS 变量
2. 在亮色和暗色主题中都要定义
3. 使用 RGB 值格式以支持透明度

```scss
:root {
  --art-custom: 100, 200, 255;
  --art-custom-rgb: var(--art-custom);
}

html.dark {
  --art-custom: 80, 160, 200;
}
```

#### 7.2.2. 创建新的组件主题

```scss
.custom-component {
  background-color: rgba(var(--art-custom), 0.1);
  border: 1px solid rgba(var(--art-custom), 0.3);
  color: rgb(var(--art-custom));

  &:hover {
    background-color: rgba(var(--art-custom), 0.2);
  }
}
```

### 7.3. 性能优化

#### 7.3.1. CSS 优化

- 避免深层嵌套选择器
- 使用 CSS 变量减少重复代码
- 合理使用 `will-change` 属性

#### 7.3.2. 动画优化

- 优先使用 `transform` 和 `opacity` 进行动画
- 避免在动画中修改布局属性
- 使用 `requestAnimationFrame` 优化 JavaScript 动画

## 8. 总结

Art Design Pro 的设计风格现代、简洁，具有高度的可定制性。其出色的 UI/UX 体现在以下几个方面：

### 8.1. 设计优势

- **一致性**: 通过全局 CSS 变量和组件化样式，确保整个应用视觉风格的一致性
- **响应式**: 完善的断点系统，适配多种设备，提供良好的跨平台体验
- **用户反馈**: 丰富的悬停效果、过渡动画和状态颜色，为用户操作提供及时的视觉反馈
- **可访问性**: 支持系统主题检测，提供色弱模式，增强应用的可访问性
- **可维护性**: 模块化的样式架构，便于维护和扩展

### 8.2. 技术特色

- **现代化**: 使用 View Transition API 实现流畅的主题切换动画
- **高性能**: 优化的 CSS 架构和动画实现，确保流畅的用户体验
- **灵活性**: 支持多种布局模式、主题模式和自定义配置
- **扩展性**: 完善的设计系统，便于添加新的组件和主题

### 8.3. 开发建议

这份文档为二次开发提供了全面的设计参考。开发者可以遵循本文档中总结的设计规范，以保持项目风格的统一，并高效地进行新功能的开发。

建议在开发过程中：

1. 严格遵循 CSS 变量使用规范
2. 保持响应式设计的一致性
3. 充分利用现有的组件样式和动画效果
4. 在添加新功能时考虑主题切换的兼容性
5. 注重性能优化和用户体验

通过遵循这些指南，可以确保项目在功能扩展的同时保持优秀的设计品质和用户体验。
