# 项目合并实施方案

## 项目概述

本方案旨在将 `E:\act-admin-pro\art-design-pro\` 项目合并到 `E:\Python\ShoppingDjango\` 项目的 `frontend` 文件夹下，用于重构后台管理系统。

### 项目基本信息

**源项目 (act-admin-pro)**
- 项目名称: art-design-pro
- 技术栈: Vue 3 + TypeScript + Element Plus + Vite
- 包管理器: pnpm
- 构建工具: Vite 6.1.0
- 主要特性: 完整的后台管理系统，包含权限管理、图表展示、表单处理等

**目标项目 (ShoppingDjango)**
- 项目名称: django-vue-hybrid-frontend
- 技术栈: Django 5.2 + Vue 3 + Vite
- 包管理器: npm
- 构建工具: Vite 5.0.0
- Vue模板分隔符: [[ ]]

## 合并目标

1. 将 act-admin-pro 项目完整迁移到 ShoppingDjango/frontend 目录
2. 统一使用 npm 作为包管理器
3. 保持 ShoppingDjango 项目的 Vite 配置为准
4. 确保 Vue 构建文件输出到 static/vue 目录
5. 保留现有模板文件作为重构参考
6. 确保项目结构清晰，无冲突

## 详细实施计划

### 阶段一：环境准备与备份 (预计时间: 30分钟)

#### 1.1 创建备份
- 备份 ShoppingDjango/frontend 目录
- 备份 ShoppingDjango/templates/pages 目录
- 备份 ShoppingDjango/templates/admin-framework.html
- 备份 ShoppingDjango/templates/logon.html
**用户要求**
备份文件至E:\back\文件夹下

#### 1.2 环境检查
- 验证 Node.js 版本兼容性 (需要 18+)
- 检查 npm 版本
- 确认 Python 和 Django 环境正常

### 阶段二：依赖分析与合并 (预计时间: 45分钟)

#### 2.1 依赖包分析

**act-admin-pro 主要依赖:**
```json
{
  "@element-plus/icons-vue": "^2.3.1",
  "@vueuse/core": "^11.0.0", 
  "@wangeditor/editor": "^5.1.23",
  "axios": "^1.7.5",
  "crypto-js": "^4.2.0",
  "echarts": "^5.6.0",
  "element-plus": "^2.10.2",
  "vue": "^3.5.12",
  "vue-router": "^4.4.2",
  "pinia": "^3.0.2",
  "typescript": "~5.6.3"
}
```

**ShoppingDjango 现有依赖:**
```json
{
  "vue": "^3.4.0",
  "@vue/runtime-dom": "^3.4.0",
  "js-md5": "^0.7.3"
}
```

#### 2.2 依赖合并策略
- 升级 Vue 到 3.5.12 版本
- 添加 TypeScript 支持
- 集成 Element Plus UI 框架
- 添加状态管理 Pinia
- 集成路由管理 Vue Router
- 保留现有的 js-md5 依赖

### 阶段三：文件结构规划 (预计时间: 20分钟)

#### 3.1 目标文件结构
```
ShoppingDjango/
├── frontend/
│   ├── src/
│   │   ├── admin/                    # 新增：后台管理模块
│   │   │   ├── views/               # act-admin-pro 的 views
│   │   │   ├── components/          # act-admin-pro 的 components
│   │   │   ├── store/               # act-admin-pro 的 store
│   │   │   ├── router/              # act-admin-pro 的 router
│   │   │   ├── api/                 # act-admin-pro 的 api
│   │   │   ├── utils/               # act-admin-pro 的 utils
│   │   │   ├── assets/              # act-admin-pro 的 assets
│   │   │   ├── types/               # act-admin-pro 的 types
│   │   │   ├── locales/             # act-admin-pro 的 locales
│   │   │   └── main.ts              # 后台管理入口文件
│   │   ├── pages/                   # 保留：原有页面组件
│   │   ├── components/              # 保留：原有通用组件
│   │   └── main.js                  # 保留：原有入口文件
│   ├── package.json                 # 合并后的依赖配置
│   ├── vite.config.js               # 更新配置支持多入口
│   ├── tsconfig.json                # 新增：TypeScript配置
│   └── eslint.config.mjs            # 新增：ESLint配置
├── static/
│   └── vue/                         # 构建输出目录
│       ├── admin/                   # 后台管理构建文件
│       └── pages/                   # 原有页面构建文件
└── templates/
    ├── pages/                       # 保留：作为重构参考
    ├── admin-framework.html         # 保留：作为重构参考
    └── logon.html                   # 保留：作为重构参考
```

### 阶段四：配置文件更新 (预计时间: 40分钟)

#### 4.1 package.json 合并
- 合并两个项目的依赖
- 统一使用 npm 脚本
- 保留 ShoppingDjango 的构建脚本逻辑

#### 4.2 Vite 配置更新
- 支持多入口构建（admin + pages）
- 保持 Vue 模板分隔符 [[ ]]
- 确保构建输出到 static/vue 目录
- 配置路径别名支持 TypeScript

#### 4.3 TypeScript 配置
- 添加 tsconfig.json
- 配置路径映射
- 设置编译选项

### 阶段五：代码迁移与适配 (预计时间: 90分钟)

#### 5.1 源码迁移
- 将 act-admin-pro/src 内容迁移到 frontend/src/admin
- 保留原有 frontend/src/pages 和 frontend/src/components
- 创建新的入口文件 frontend/src/admin/main.ts

#### 5.2 模板分隔符适配
- 将所有 Vue 模板中的 {{ }} 替换为 [[ ]]
- 更新 Vite 配置确保编译时使用正确分隔符

#### 5.3 路径引用更新
- 更新所有 import 路径
- 适配新的目录结构
- 确保资源文件路径正确

### 阶段六：构建配置优化 (预计时间: 30分钟)

#### 6.1 多入口配置
- 配置 admin 和 pages 两个入口
- 确保构建文件分离
- 优化 chunk 分割策略

#### 6.2 输出目录配置
- admin 相关文件输出到 static/vue/admin/
- pages 相关文件输出到 static/vue/pages/
- 共享依赖合理分割

### 阶段七：测试与验证 (预计时间: 45分钟)

#### 7.1 构建测试
- 执行 npm run build 验证构建成功
- 检查输出文件结构
- 验证文件大小合理

#### 7.2 功能测试
- 测试后台管理页面加载
- 验证路由功能正常
- 检查组件渲染正确

#### 7.3 集成测试
- 与 Django 后端集成测试
- 验证静态文件服务正常
- 确认 API 调用正常

## 风险评估与应对

### 高风险项
1. **依赖版本冲突**
   - 风险：Vue 版本升级可能导致现有代码不兼容
   - 应对：逐步升级，充分测试

2. **构建配置复杂化**
   - 风险：多入口配置可能导致构建失败
   - 应对：分步配置，逐个验证

### 中风险项
1. **模板分隔符替换**
   - 风险：可能遗漏部分模板语法
   - 应对：使用正则表达式批量替换，人工复查

2. **路径引用错误**
   - 风险：迁移后路径错误导致模块加载失败
   - 应对：使用 IDE 重构功能，批量更新

### 低风险项
1. **样式冲突**
   - 风险：两个项目的样式可能冲突
   - 应对：使用 CSS 模块化，命名空间隔离

## 质量保证措施

### 代码质量
- 保持原有代码结构和逻辑
- 遵循 TypeScript 最佳实践
- 使用 ESLint 和 Prettier 统一代码风格

### 安全性
- 验证所有依赖包的安全性
- 确保没有引入恶意代码
- 保持最小权限原则

### 性能优化
- 合理配置 chunk 分割
- 启用 gzip 压缩
- 优化资源加载策略

## 实施时间表

| 阶段 | 任务 | 预计时间 | 负责人 | 状态 |
|------|------|----------|--------|------|
| 1 | 环境准备与备份 | 30分钟 | 开发者 | 待开始 |
| 2 | 依赖分析与合并 | 45分钟 | 开发者 | 待开始 |
| 3 | 文件结构规划 | 20分钟 | 开发者 | 待开始 |
| 4 | 配置文件更新 | 40分钟 | 开发者 | 待开始 |
| 5 | 代码迁移与适配 | 90分钟 | 开发者 | 待开始 |
| 6 | 构建配置优化 | 30分钟 | 开发者 | 待开始 |
| 7 | 测试与验证 | 45分钟 | 开发者 | 待开始 |

**总计预估时间：5小时**

## 成功标准

1. ✅ 项目合并完成，无构建错误
2. ✅ 后台管理功能正常运行
3. ✅ 原有前台功能不受影响
4. ✅ 代码结构清晰，易于维护
5. ✅ 构建产物大小合理
6. ✅ 与 Django 后端集成正常

## 详细技术实施步骤

### 步骤1：环境准备与备份

#### 1.1 创建备份目录
```bash
# 在 E:\ 根目录创建备份文件夹
mkdir E:\back
cd E:\back
mkdir $(date +%Y%m%d_%H%M%S)_project_merge_backup
BACKUP_DIR="E:\back\$(date +%Y%m%d_%H%M%S)_project_merge_backup"

# 备份 ShoppingDjango 关键目录和文件
cd E:\Python\ShoppingDjango
cp -r frontend/ $BACKUP_DIR/frontend_backup/
cp -r templates/pages/ $BACKUP_DIR/templates_pages_backup/
cp templates/admin-framework.html $BACKUP_DIR/
cp templates/logon.html $BACKUP_DIR/

# 备份 act-admin-pro 项目
cd E:\act-admin-pro\art-design-pro
cp -r . $BACKUP_DIR/act_admin_pro_backup/
```

#### 1.2 环境检查脚本
```bash
# 检查 Node.js 版本
echo "检查 Node.js 版本..."
node --version  # 应该 >= 18.0.0

# 检查 npm 版本
echo "检查 npm 版本..."
npm --version   # 应该 >= 8.0.0

# 检查 Python 和 Django
echo "检查 Python 和 Django..."
cd E:\Python\ShoppingDjango
python --version  # 应该 >= 3.8
python -c "import django; print('Django版本:', django.get_version())"  # 应该 >= 5.2

# 检查现有前端环境
echo "检查现有前端环境..."
cd frontend
npm list vue
npm list vite
```

### 步骤2：依赖包合并配置

#### 2.1 新的 package.json 配置
```json
{
  "name": "django-vue-hybrid-frontend",
  "version": "2.0.0",
  "description": "Vue.js frontend for Django hybrid rendering system with admin panel",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "dev:admin": "vite --mode admin",
    "build": "vite build",
    "build:admin": "vite build --mode admin",
    "build:all": "npm run build && npm run build:admin",
    "preview": "vite preview",
    "build:watch": "vite build --watch",
    "serve": "concurrently \"npm run dev\" \"cd .. && python manage.py runserver\"",
    "lint": "eslint src --ext .vue,.js,.ts,.jsx,.tsx",
    "lint:fix": "eslint src --ext .vue,.js,.ts,.jsx,.tsx --fix",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "@element-plus/icons-vue": "^2.3.1",
    "@vue/runtime-dom": "^3.5.12",
    "@vueuse/core": "^11.0.0",
    "@wangeditor/editor": "^5.1.23",
    "@wangeditor/editor-for-vue": "next",
    "axios": "^1.7.5",
    "crypto-js": "^4.2.0",
    "echarts": "^5.6.0",
    "element-plus": "^2.10.2",
    "file-saver": "^2.0.5",
    "highlight.js": "^11.10.0",
    "js-md5": "^0.7.3",
    "md-editor-v3": "^4.17.0",
    "mitt": "^3.0.1",
    "nprogress": "^0.2.0",
    "pinia": "^3.0.2",
    "pinia-plugin-persistedstate": "^4.3.0",
    "qrcode.vue": "^3.6.0",
    "vue": "^3.5.12",
    "vue-draggable-plus": "^0.6.0",
    "vue-i18n": "^9.14.0",
    "vue-img-cutter": "^3.0.5",
    "vue-router": "^4.4.2",
    "xgplayer": "^3.0.20",
    "xlsx": "^0.18.5"
  },
  "devDependencies": {
    "@babel/core": "^7.23.0",
    "@babel/preset-env": "^7.23.0",
    "@types/node": "^22.1.0",
    "@typescript-eslint/eslint-plugin": "^8.3.0",
    "@typescript-eslint/parser": "^8.3.0",
    "@vitejs/plugin-vue": "^5.2.1",
    "autoprefixer": "^10.4.0",
    "concurrently": "^8.2.0",
    "eslint": "^9.9.1",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-prettier": "^5.2.1",
    "eslint-plugin-vue": "^9.27.0",
    "globals": "^15.9.0",
    "postcss": "^8.4.0",
    "prettier": "^3.5.3",
    "rollup-plugin-visualizer": "^5.12.0",
    "sass": "^1.81.0",
    "stylelint": "^16.20.0",
    "stylelint-config-html": "^1.1.0",
    "stylelint-config-recess-order": "^4.6.0",
    "stylelint-config-recommended-scss": "^14.1.0",
    "stylelint-config-recommended-vue": "^1.5.0",
    "stylelint-config-standard": "^36.0.1",
    "terser": "^5.43.1",
    "tsx": "^4.20.3",
    "typescript": "~5.6.3",
    "typescript-eslint": "^8.9.0",
    "unplugin-auto-import": "^0.18.3",
    "unplugin-vue-components": "^0.27.4",
    "vite": "^6.1.0",
    "vite-plugin-compression": "^0.5.1",
    "vite-plugin-imagemin": "^0.6.1",
    "vite-plugin-vue-devtools": "^7.7.6",
    "vue-demi": "^0.14.9",
    "vue-tsc": "~2.1.6"
  },
  "browserslist": [
    "> 1%",
    "last 2 versions",
    "not dead",
    "not ie 11"
  ]
}
```

#### 2.2 TypeScript 配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@admin/*": ["src/admin/*"],
      "@pages/*": ["src/pages/*"],
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/admin/utils/*"],
      "@stores/*": ["src/admin/store/*"],
      "@views/*": ["src/admin/views/*"],
      "@api/*": ["src/admin/api/*"],
      "@styles/*": ["src/admin/assets/styles/*"],
      "@imgs/*": ["src/admin/assets/img/*"],
      "@icons/*": ["src/admin/assets/icons/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 步骤3：Vite 配置更新

#### 3.1 更新后的 vite.config.js
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { fileURLToPath } from 'url'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import viteCompression from 'vite-plugin-compression'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 扫描Vue文件的函数（保留原有逻辑）
function scanVueFiles(dir) {
  const fs = require('fs')
  const files = {}

  function scan(currentDir, relativePath = '') {
    const items = fs.readdirSync(currentDir)

    items.forEach(item => {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        scan(fullPath, path.join(relativePath, item))
      } else if (item.endsWith('.vue')) {
        const key = path.join(relativePath, item.replace('.vue', '')).replace(/\\/g, '/')
        files[key] = fullPath
      }
    })
  }

  if (fs.existsSync(dir)) {
    scan(dir)
  }

  return files
}

// Vue入口生成器插件（保留原有逻辑并适配新结构）
function vueEntryGeneratorPlugin() {
  return {
    name: 'vue-entry-generator',
    resolveId(id) {
      if (id.startsWith('virtual:vue-entry:')) {
        return id
      }
    },
    load(id) {
      if (id.startsWith('virtual:vue-entry:')) {
        const componentKey = id.replace('virtual:vue-entry:', '')
        const vueFiles = scanVueFiles(path.resolve(__dirname, 'src/pages'))
        const componentPath = vueFiles[componentKey]

        if (componentPath) {
          return `
import { createApp } from 'vue'
import Component from '${componentPath}'

// 创建Vue应用实例
const app = createApp(Component)

// 注入Django上下文数据
if (window.__DJANGO_CONTEXT__) {
  app.provide('djangoContext', window.__DJANGO_CONTEXT__)

  // 将Django数据混入到组件的data中
  const originalData = Component.data || (() => ({}))
  Component.data = function() {
    const data = originalData.call(this)
    return {
      ...data,
      djangoContext: window.__DJANGO_CONTEXT__
    }
  }
}

// 等待DOM加载完成后挂载
document.addEventListener('DOMContentLoaded', () => {
  const mountPoint = document.getElementById('app')
  if (mountPoint) {
    app.mount('#app')
    console.log('[Vue] 应用已成功挂载')
  } else {
    console.error('[Vue] 未找到挂载点 #app')
  }
})

// 导出应用实例供调试使用
window.__VUE_APP__ = app
`
        }
      }
    }
  }
}

// 生成虚拟入口映射
function generateVueEntries(dir) {
  const entries = {}
  const vueFiles = scanVueFiles(dir)

  Object.keys(vueFiles).forEach(key => {
    entries[key] = `virtual:vue-entry:${key}`
  })

  return entries
}

export default defineConfig(({ mode }) => {
  const isAdmin = mode === 'admin'

  return {
    plugins: [
      vue({
        template: {
          compilerOptions: {
            delimiters: ['[[', ']]']  // 保持Django兼容的分隔符
          }
        }
      }),
      // 自动导入组件
      Components({
        deep: true,
        extensions: ['vue'],
        dirs: [
          'src/components',
          'src/admin/components'
        ],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/components.d.ts'
      }),
      // 自动导入API
      AutoImport({
        imports: ['vue', 'vue-router', '@vueuse/core', 'pinia'],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/auto-imports.d.ts'
      }),
      // 压缩插件
      viteCompression({
        verbose: true,
        disable: false,
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 10240,
        deleteOriginFile: false
      }),
      // Vue入口生成器（仅在非admin模式下使用）
      !isAdmin && vueEntryGeneratorPlugin()
    ].filter(Boolean),

    root: path.resolve(__dirname, 'src'),

    build: {
      outDir: isAdmin
        ? path.resolve(__dirname, '../static/vue/admin')
        : path.resolve(__dirname, '../static/vue/pages'),
      emptyOutDir: false,

      rollupOptions: {
        input: isAdmin ? {
          // 后台管理入口
          admin: path.resolve(__dirname, 'src/admin/main.ts')
        } : {
          // 原有页面入口（保留原有逻辑）
          ...generateVueEntries(path.resolve(__dirname, 'src/pages'))
        },

        output: {
          entryFileNames: '[name].js',
          chunkFileNames: 'chunks/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
          manualChunks: isAdmin ? {
            vendor: ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus', '@element-plus/icons-vue'],
            utils: ['axios', '@vueuse/core', 'crypto-js']
          } : undefined
        }
      },

      manifest: true,
      minify: 'terser',
      sourcemap: false,
      target: 'es2015',

      commonjsOptions: {
        include: [/js-md5/, /node_modules/]
      }
    },

    server: {
      port: 5173,
      host: 'localhost',
      proxy: {
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        },
        '/static': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        },
        '/media': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        },
        '/admin': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        }
      },
      hmr: {
        port: 5174,
        host: 'localhost'
      },
      cors: true,
      open: false
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@admin': path.resolve(__dirname, 'src/admin'),
        '@pages': path.resolve(__dirname, 'src/pages'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@utils': path.resolve(__dirname, 'src/admin/utils'),
        '@stores': path.resolve(__dirname, 'src/admin/store'),
        '@views': path.resolve(__dirname, 'src/admin/views'),
        '@api': path.resolve(__dirname, 'src/admin/api'),
        '@styles': path.resolve(__dirname, 'src/admin/assets/styles'),
        '@imgs': path.resolve(__dirname, 'src/admin/assets/img'),
        '@icons': path.resolve(__dirname, 'src/admin/assets/icons'),
        'vue': 'vue/dist/vue.esm-bundler.js'
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `
            @use "@styles/variables.scss" as *;
            @use "@styles/mixin.scss" as *;
          `
        }
      }
    },

    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false
    }
  }
})
```

### 步骤4：文件迁移执行计划

#### 4.1 创建新的目录结构
```bash
# 在 E:\Python\ShoppingDjango\frontend\src 目录下执行
cd E:\Python\ShoppingDjango\frontend\src
mkdir -p admin/{views,components,store,router,api,utils,assets,types,locales,composables,directives,config,enums,mock}
mkdir -p admin/assets/{styles,img,icons,svg,fonts}
```

#### 4.2 文件迁移映射表

| 源路径 (act-admin-pro) | 目标路径 (ShoppingDjango) | 说明 |
|------------------------|---------------------------|------|
| `src/views/` | `frontend/src/admin/views/` | 后台管理页面 |
| `src/components/` | `frontend/src/admin/components/` | 后台管理组件 |
| `src/store/` | `frontend/src/admin/store/` | 状态管理 |
| `src/router/` | `frontend/src/admin/router/` | 路由配置 |
| `src/api/` | `frontend/src/admin/api/` | API接口 |
| `src/utils/` | `frontend/src/admin/utils/` | 工具函数 |
| `src/assets/` | `frontend/src/admin/assets/` | 静态资源 |
| `src/types/` | `frontend/src/admin/types/` | 类型定义 |
| `src/locales/` | `frontend/src/admin/locales/` | 国际化 |
| `src/composables/` | `frontend/src/admin/composables/` | 组合式函数 |
| `src/directives/` | `frontend/src/admin/directives/` | 自定义指令 |
| `src/config/` | `frontend/src/admin/config/` | 配置文件 |
| `src/enums/` | `frontend/src/admin/enums/` | 枚举定义 |
| `src/mock/` | `frontend/src/admin/mock/` | 模拟数据 |
| `src/main.ts` | `frontend/src/admin/main.ts` | 入口文件 |
| `src/App.vue` | `frontend/src/admin/App.vue` | 根组件 |

#### 4.3 批量文件迁移脚本
```bash
#!/bin/bash
# 文件迁移脚本 migrate_files.sh

SOURCE_DIR="E:\act-admin-pro\art-design-pro\src"
TARGET_DIR="E:\Python\ShoppingDjango\frontend\src\admin"

echo "开始迁移文件..."

# 迁移主要目录
cp -r "$SOURCE_DIR/views" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/components" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/store" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/router" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/api" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/utils" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/assets" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/types" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/locales" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/composables" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/directives" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/config" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/enums" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/mock" "$TARGET_DIR/"

# 迁移主要文件
cp "$SOURCE_DIR/main.ts" "$TARGET_DIR/"
cp "$SOURCE_DIR/App.vue" "$TARGET_DIR/"
cp "$SOURCE_DIR/env.d.ts" "$TARGET_DIR/"

echo "文件迁移完成！"
```

#### 4.4 模板分隔符替换脚本
```bash
#!/bin/bash
# 模板分隔符替换脚本 replace_delimiters.sh

TARGET_DIR="E:\Python\ShoppingDjango\frontend\src\admin"

echo "开始替换Vue模板分隔符..."

# 递归查找所有.vue文件并替换模板分隔符
find "$TARGET_DIR" -name "*.vue" -type f -exec sed -i 's/{{/[[/g; s/}}/]]/g' {} \;

# 检查替换结果
echo "模板分隔符替换完成，以下文件已更新："
find "$TARGET_DIR" -name "*.vue" -type f -exec grep -l "\[\[.*\]\]" {} \;

echo "请手动检查以下可能需要特殊处理的文件："
find "$TARGET_DIR" -name "*.vue" -type f -exec grep -l "v-html\|v-text" {} \;
```

### 步骤5：关键文件适配

#### 5.1 新的后台管理入口文件 (frontend/src/admin/main.ts)
```typescript
import { createApp } from 'vue'
import App from './App.vue'
import { initStore } from './store'
import { initRouter } from './router'
import '@styles/reset.scss'
import '@styles/app.scss'
import '@styles/el-ui.scss'
import '@styles/mobile.scss'
import '@styles/change.scss'
import '@styles/theme-animation.scss'
import '@styles/el-light.scss'
import '@styles/el-dark.scss'
import '@styles/dark.scss'
import '@icons/system/iconfont.js'
import '@icons/system/iconfont.css'
import '@utils/sys/console.ts'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { setupGlobDirectives } from './directives'
import language from './locales'

// 适配 Django 环境
const app = createApp(App)

// 初始化 Store 和 Router
initStore(app)
initRouter(app)
setupGlobDirectives(app)

// 国际化
app.use(language)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注入Django上下文数据（保持与原有系统兼容）
if (window.__DJANGO_CONTEXT__) {
  app.provide('djangoContext', window.__DJANGO_CONTEXT__)
}

// 等待DOM加载完成后挂载到后台管理容器
document.addEventListener('DOMContentLoaded', () => {
  const mountPoint = document.getElementById('admin-app') || document.getElementById('app')
  if (mountPoint) {
    app.mount(mountPoint)
    console.log('[Vue Admin] 后台管理应用已成功挂载')
  } else {
    console.error('[Vue Admin] 未找到挂载点 #admin-app 或 #app')
  }
})

// 导出应用实例供调试使用
window.__VUE_ADMIN_APP__ = app
```

#### 5.2 路径引用更新脚本
```bash
#!/bin/bash
# 路径引用更新脚本 update_imports.sh

TARGET_DIR="E:\Python\ShoppingDjango\frontend\src\admin"

echo "开始更新import路径引用..."

# 更新相对路径引用
find "$TARGET_DIR" -name "*.ts" -o -name "*.vue" -o -name "*.js" | xargs sed -i \
  -e 's|@/|@admin/|g' \
  -e 's|@views|@admin/views|g' \
  -e 's|@components|@admin/components|g' \
  -e 's|@utils|@admin/utils|g' \
  -e 's|@stores|@admin/store|g' \
  -e 's|@api|@admin/api|g' \
  -e 's|@styles|@admin/assets/styles|g' \
  -e 's|@imgs|@admin/assets/img|g' \
  -e 's|@icons|@admin/assets/icons|g'

echo "路径引用更新完成！"
```

### 步骤6：Django 模板集成

#### 6.1 新的后台管理模板 (templates/admin/base.html)
```html
<!DOCTYPE html>
{% load static %}
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}后台管理系统{% endblock %}</title>
    <meta name="csrf-token" content="{{ csrf_token }}">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="{% static 'vue/admin/admin.js' %}" as="script">
    <link rel="preload" href="{% static 'vue/admin/assets/admin.css' %}" as="style">

    <!-- 样式文件 -->
    <link rel="stylesheet" href="{% static 'vue/admin/assets/admin.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Django 上下文数据注入 -->
    <script>
        window.__DJANGO_CONTEXT__ = {
            user: {
                id: {{ user.id|default:'null' }},
                username: "{{ user.username|default:'' }}",
                email: "{{ user.email|default:'' }}",
                is_staff: {{ user.is_staff|yesno:'true,false' }},
                is_superuser: {{ user.is_superuser|yesno:'true,false' }}
            },
            csrf_token: "{{ csrf_token }}",
            static_url: "{{ STATIC_URL }}",
            media_url: "{{ MEDIA_URL }}",
            debug: {{ settings.DEBUG|yesno:'true,false' }}
        };
    </script>

    <!-- Vue 应用挂载点 -->
    <div id="admin-app">
        <!-- 加载中的占位内容 -->
        <div class="loading-placeholder">
            <div class="loading-spinner"></div>
            <p>正在加载后台管理系统...</p>
        </div>
    </div>

    <!-- CSRF Token -->
    <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

    <!-- Vue 应用脚本 -->
    <script type="module" src="{% static 'vue/admin/admin.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
```

#### 6.2 Django URL 配置更新
```python
# app/urls.py 更新
from django.urls import path, include
from . import views

app_name = 'app'

urlpatterns = [
    # 后台管理首页
    path('', views.admin_dashboard, name='dashboard'),

    # 后台管理各个模块
    path('users/', views.admin_users, name='users'),
    path('products/', views.admin_products, name='products'),
    path('orders/', views.admin_orders, name='orders'),
    path('settings/', views.admin_settings, name='settings'),

    # API 接口
    path('api/', include('api.urls')),
]
```

#### 6.3 Django 视图函数更新
```python
# app/views.py 更新
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required

@staff_member_required
def admin_dashboard(request):
    """后台管理首页"""
    return render(request, 'admin/base.html', {
        'title': '仪表盘'
    })

@staff_member_required
def admin_users(request):
    """用户管理"""
    return render(request, 'admin/base.html', {
        'title': '用户管理'
    })

@staff_member_required
def admin_products(request):
    """商品管理"""
    return render(request, 'admin/base.html', {
        'title': '商品管理'
    })

@staff_member_required
def admin_orders(request):
    """订单管理"""
    return render(request, 'admin/base.html', {
        'title': '订单管理'
    })

@staff_member_required
def admin_settings(request):
    """系统设置"""
    return render(request, 'admin/base.html', {
        'title': '系统设置'
    })
```

### 步骤7：构建和测试验证

#### 7.1 构建脚本执行
```bash
# 在 E:\Python\ShoppingDjango\frontend 目录下执行

echo "开始安装依赖..."
npm install

echo "开始构建页面组件..."
npm run build

echo "开始构建后台管理..."
npm run build:admin

echo "检查构建结果..."
ls -la ../static/vue/
ls -la ../static/vue/admin/
ls -la ../static/vue/pages/
```

#### 7.2 功能测试清单
```bash
# 测试清单 test_checklist.md

## 构建测试
- [ ] npm install 成功执行
- [ ] npm run build 成功执行
- [ ] npm run build:admin 成功执行
- [ ] static/vue/admin/ 目录存在且包含构建文件
- [ ] static/vue/pages/ 目录存在且包含构建文件
- [ ] 构建文件大小合理（admin.js < 2MB）

## 开发环境测试
- [ ] npm run dev 成功启动
- [ ] npm run dev:admin 成功启动
- [ ] 热更新功能正常
- [ ] 浏览器控制台无错误

## Django 集成测试
- [ ] Django 服务器正常启动
- [ ] 后台管理页面可以访问
- [ ] Vue 应用正确挂载
- [ ] Django 上下文数据正确注入
- [ ] CSRF Token 正确传递
- [ ] API 请求正常工作

## 功能测试
- [ ] 用户登录功能正常
- [ ] 路由跳转正常
- [ ] 组件渲染正确
- [ ] 表单提交正常
- [ ] 数据展示正确
- [ ] 权限控制有效

## 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] 首屏渲染时间 < 1秒
- [ ] 资源文件大小合理
- [ ] 网络请求数量合理
```

#### 7.3 问题排查指南
```bash
# 常见问题排查指南

## 构建失败
1. 检查 Node.js 版本是否 >= 18
2. 删除 node_modules 重新安装
3. 检查 package.json 语法是否正确
4. 查看构建日志中的具体错误信息

## 页面无法加载
1. 检查 Django 静态文件配置
2. 确认构建文件是否存在
3. 检查浏览器控制台错误
4. 验证 URL 路径是否正确

## Vue 应用无法挂载
1. 检查挂载点 ID 是否正确
2. 确认 Django 模板是否正确引入脚本
3. 检查 CSRF Token 是否正确传递
4. 验证 Vue 应用入口文件是否正确

## API 请求失败
1. 检查 Django URL 配置
2. 确认 CSRF Token 配置
3. 验证请求头设置
4. 检查权限装饰器
```

## 验收标准

### 技术验收
1. ✅ 所有构建命令执行成功，无错误
2. ✅ 静态文件正确输出到指定目录
3. ✅ Vue 应用在 Django 环境中正常运行
4. ✅ 前后端数据交互正常
5. ✅ 代码结构清晰，符合最佳实践

### 功能验收
1. ✅ 后台管理系统完整功能可用
2. ✅ 用户认证和权限控制正常
3. ✅ 原有前台功能不受影响
4. ✅ 响应式设计在各设备正常显示
5. ✅ 国际化功能正常工作

### 性能验收
1. ✅ 页面加载时间 < 3秒
2. ✅ 构建文件大小合理
3. ✅ 内存使用正常
4. ✅ 无内存泄漏
5. ✅ 浏览器兼容性良好

## 后续计划

合并完成后的后续工作：
1. 删除备份的模板文件
2. 重构后台管理界面
3. 集成 Django 用户认证
4. 优化 API 接口设计
5. 完善文档和测试用例

---

**重要提醒：**
- 本实施方案已经过详细规划和风险评估
- 所有操作都有完整的备份和回滚机制
- 严格按照步骤执行，确保每个阶段验证通过后再进行下一步
- 如遇到任何问题，立即停止并寻求技术支持
- 项目合并完成后需要进行全面测试验证

## 详细技术实施步骤

### 步骤1：环境准备与备份

#### 1.1 创建备份目录
```bash
# 在 ShoppingDjango 根目录执行
mkdir -p backups/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

# 备份关键目录和文件
cp -r frontend/ $BACKUP_DIR/frontend_backup/
cp -r templates/pages/ $BACKUP_DIR/templates_pages_backup/
cp templates/admin-framework.html $BACKUP_DIR/
cp templates/logon.html $BACKUP_DIR/
```

#### 1.2 环境检查脚本
```bash
# 检查 Node.js 版本
node --version  # 应该 >= 18.0.0

# 检查 npm 版本
npm --version   # 应该 >= 8.0.0

# 检查 Python 和 Django
python --version  # 应该 >= 3.8
python -c "import django; print(django.get_version())"  # 应该 >= 5.2
```

### 步骤2：依赖包合并配置

#### 2.1 新的 package.json 配置
```json
{
  "name": "django-vue-hybrid-frontend",
  "version": "2.0.0",
  "description": "Vue.js frontend for Django hybrid rendering system with admin panel",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "dev:admin": "vite --mode admin",
    "build": "vite build",
    "build:admin": "vite build --mode admin",
    "build:all": "npm run build && npm run build:admin",
    "preview": "vite preview",
    "build:watch": "vite build --watch",
    "serve": "concurrently \"npm run dev\" \"cd .. && python manage.py runserver\"",
    "lint": "eslint src --ext .vue,.js,.ts,.jsx,.tsx",
    "lint:fix": "eslint src --ext .vue,.js,.ts,.jsx,.tsx --fix",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "@element-plus/icons-vue": "^2.3.1",
    "@vue/runtime-dom": "^3.5.12",
    "@vueuse/core": "^11.0.0",
    "@wangeditor/editor": "^5.1.23",
    "@wangeditor/editor-for-vue": "next",
    "axios": "^1.7.5",
    "crypto-js": "^4.2.0",
    "echarts": "^5.6.0",
    "element-plus": "^2.10.2",
    "file-saver": "^2.0.5",
    "js-md5": "^0.7.3",
    "mitt": "^3.0.1",
    "nprogress": "^0.2.0",
    "pinia": "^3.0.2",
    "pinia-plugin-persistedstate": "^4.3.0",
    "vue": "^3.5.12",
    "vue-draggable-plus": "^0.6.0",
    "vue-i18n": "^9.14.0",
    "vue-router": "^4.4.2",
    "xlsx": "^0.18.5"
  },
  "devDependencies": {
    "@babel/core": "^7.23.0",
    "@babel/preset-env": "^7.23.0",
    "@types/node": "^22.1.0",
    "@typescript-eslint/eslint-plugin": "^8.3.0",
    "@typescript-eslint/parser": "^8.3.0",
    "@vitejs/plugin-vue": "^5.2.1",
    "autoprefixer": "^10.4.0",
    "concurrently": "^8.2.0",
    "eslint": "^9.9.1",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-prettier": "^5.2.1",
    "eslint-plugin-vue": "^9.27.0",
    "postcss": "^8.4.0",
    "prettier": "^3.5.3",
    "sass": "^1.81.0",
    "terser": "^5.43.1",
    "typescript": "~5.6.3",
    "unplugin-auto-import": "^0.18.3",
    "unplugin-vue-components": "^0.27.4",
    "vite": "^6.1.0",
    "vite-plugin-compression": "^0.5.1",
    "vue-tsc": "~2.1.6"
  }
}
```

#### 2.2 TypeScript 配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@admin/*": ["src/admin/*"],
      "@pages/*": ["src/pages/*"],
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/admin/utils/*"],
      "@stores/*": ["src/admin/store/*"],
      "@views/*": ["src/admin/views/*"],
      "@api/*": ["src/admin/api/*"],
      "@styles/*": ["src/admin/assets/styles/*"],
      "@imgs/*": ["src/admin/assets/img/*"],
      "@icons/*": ["src/admin/assets/icons/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 步骤3：Vite 配置更新

#### 3.1 更新后的 vite.config.js
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { fileURLToPath } from 'url'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import viteCompression from 'vite-plugin-compression'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 扫描Vue文件的函数（保留原有逻辑）
function scanVueFiles(dir) {
  // ... 保留原有实现
}

// Vue入口生成器插件（保留原有逻辑）
function vueEntryGeneratorPlugin() {
  // ... 保留原有实现，但需要适配新的目录结构
}

export default defineConfig(({ mode }) => {
  const isAdmin = mode === 'admin'

  return {
    plugins: [
      vue({
        template: {
          compilerOptions: {
            delimiters: ['[[', ']]']  // 保持Django兼容的分隔符
          }
        }
      }),
      // 自动导入组件
      Components({
        deep: true,
        extensions: ['vue'],
        dirs: [
          'src/components',
          'src/admin/components'
        ],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/components.d.ts'
      }),
      // 自动导入API
      AutoImport({
        imports: ['vue', 'vue-router', '@vueuse/core', 'pinia'],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/auto-imports.d.ts'
      }),
      // 压缩插件
      viteCompression({
        verbose: true,
        disable: false,
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 10240,
        deleteOriginFile: false
      }),
      // 保留原有的Vue入口生成器
      vueEntryGeneratorPlugin()
    ],

    root: path.resolve(__dirname, 'src'),

    build: {
      outDir: isAdmin
        ? path.resolve(__dirname, '../static/vue/admin')
        : path.resolve(__dirname, '../static/vue/pages'),
      emptyOutDir: false,

      rollupOptions: {
        input: isAdmin ? {
          // 后台管理入口
          admin: path.resolve(__dirname, 'src/admin/main.ts')
        } : {
          // 原有页面入口（保留原有逻辑）
          ...generateVueEntries(path.resolve(__dirname, 'src/pages'))
        },

        output: {
          entryFileNames: '[name].js',
          chunkFileNames: 'chunks/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
          manualChunks: isAdmin ? {
            vendor: ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus', '@element-plus/icons-vue'],
            utils: ['axios', '@vueuse/core', 'crypto-js']
          } : undefined
        }
      },

      manifest: true,
      minify: 'terser',
      sourcemap: false,
      target: 'es2015'
    },

    server: {
      port: 5173,
      host: 'localhost',
      proxy: {
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        },
        '/static': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        },
        '/admin': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false
        }
      },
      hmr: {
        port: 5174,
        host: 'localhost'
      },
      cors: true,
      open: false
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@admin': path.resolve(__dirname, 'src/admin'),
        '@pages': path.resolve(__dirname, 'src/pages'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@utils': path.resolve(__dirname, 'src/admin/utils'),
        '@stores': path.resolve(__dirname, 'src/admin/store'),
        '@views': path.resolve(__dirname, 'src/admin/views'),
        '@api': path.resolve(__dirname, 'src/admin/api'),
        '@styles': path.resolve(__dirname, 'src/admin/assets/styles'),
        '@imgs': path.resolve(__dirname, 'src/admin/assets/img'),
        '@icons': path.resolve(__dirname, 'src/admin/assets/icons'),
        'vue': 'vue/dist/vue.esm-bundler.js'
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `
            @use "@styles/variables.scss" as *;
            @use "@styles/mixin.scss" as *;
          `
        }
      }
    },

    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false
    }
  }
})
```

---

**注意事项：**
- 本方案需要在用户确认后才能开始执行
- 执行过程中如遇到问题，应立即停止并寻求用户指导
- 所有操作都应该有备份，确保可以回滚
- 严格按照 RIPER-5 协议标准执行，确保生产环境质量
