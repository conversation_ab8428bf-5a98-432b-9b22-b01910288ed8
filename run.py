#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import subprocess
import threading
import socket
import json
import time
import signal
import queue
import django
import importlib.util
import pkg_resources
import requests
import shutil
import getpass
import platform
from pathlib import Path
from django.core.management import execute_from_command_line
from django.core.management.base import CommandError

# 全局变量，用于控制WebSocket服务器线程
ws_server_running = False
ws_server_thread = None
ws_clients = {}  # {client_id: {'socket': socket, 'addr': addr, 'threads': []}}
ws_client_counter = 0  # 用于生成客户端ID
ws_client_lock = threading.Lock()  # 用于保护ws_clients和ws_client_counter的线程锁

# 全局变量，用于控制过期订单监控线程
order_monitor_running = False
order_monitor_thread = None
order_monitor_interval = 3600  # 默认每小时检查一次（3600秒）
order_expiry_hours = 1  # 默认订单过期时间为1小时

# 全局变量，用于控制Vue开发服务器线程
vite_server_running = False
vite_server_thread = None
django_server_process = None

# 事件队列
connection_event_queue = queue.Queue()
message_event_queue = queue.Queue()
disconnection_event_queue = queue.Queue()

def start_websocket_server():
    """
    启动一个简单的WebSocket服务器，固定使用9000端口
    """
    global ws_server_running, ws_server_thread
    
    print("启动WebSocket服务器，端口: 9000")
    
    def websocket_thread():
        """WebSocket服务器线程函数"""
        try:
            # 创建WebSocket服务器
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            # 设置非阻塞模式
            server_socket.setblocking(False)
            server_socket.bind(('0.0.0.0', 9000))
            server_socket.listen(5)
            
            print("WebSocket服务器已启动，端口: 9000")
            
            # 使用非阻塞轮询而不是accept阻塞
            while ws_server_running:
                try:
                    # 非阻塞accept
                    client_socket, addr = server_socket.accept()
                    client_socket.setblocking(False)
                    
                    # 创建一个新线程处理握手
                    handshake_thread = threading.Thread(target=handle_new_connection, args=(client_socket, addr))
                    handshake_thread.daemon = True
                    handshake_thread.start()
                    
                except BlockingIOError:
                    # 没有新连接，正常情况
                    time.sleep(0.1)  # 短暂休眠以减少CPU使用
                except Exception as e:
                    if ws_server_running:  # 只有在服务器运行时才打印错误
                        print(f"WebSocket服务器接受连接错误: {e}")
                    time.sleep(0.1)
            
            # 清理所有客户端连接
            with ws_client_lock:
                for client_id, client_info in list(ws_clients.items()):
                    try:
                        client_info['socket'].close()
                    except:
                        pass
                ws_clients.clear()
                
        except Exception as e:
            print(f"WebSocket服务器错误: {e}")
        finally:
            print("WebSocket服务器已关闭")
            try:
                server_socket.close()
            except:
                pass
    
    def handle_new_connection(client_socket, addr):
        """处理新的WebSocket连接"""
        global ws_client_counter
        
        try:
            # 握手阶段使用阻塞模式
            client_socket.setblocking(True)
            
            # WebSocket握手
            data = client_socket.recv(4096).decode('utf-8')
            if not data.startswith("GET"):
                client_socket.close()
                return
            
            response = (
                "HTTP/1.1 101 Switching Protocols\r\n"
                "Upgrade: websocket\r\n"
                "Connection: Upgrade\r\n"
                "Sec-WebSocket-Accept: s3pPLMBiTxaQ9kYGzzhZRbK+xOo=\r\n\r\n"
            )
            client_socket.send(response.encode('utf-8'))
            
            # 重新设置为非阻塞模式
            client_socket.setblocking(False)
            
            # 生成新的客户端ID并存储连接信息
            with ws_client_lock:
                client_id = ws_client_counter
                ws_client_counter += 1
                ws_clients[client_id] = {
                    'socket': client_socket,
                    'addr': addr,
                    'threads': []
                }
            
            # 启动事件处理线程
            message_thread = threading.Thread(target=handle_client_messages, args=(client_id,))
            message_thread.daemon = True
            message_thread.start()
            
            with ws_client_lock:
                if client_id in ws_clients:
                    ws_clients[client_id]['threads'].append(message_thread)
            
            # 将连接成功事件添加到队列
            connection_event_queue.put({
                'type': 'connection',
                'client_id': client_id,
                'addr': addr,
                'timestamp': time.time()
            })
            
            # 启动连接事件处理线程
            handle_connection_event_thread = threading.Thread(target=handle_connection_event, args=(client_id, addr))
            handle_connection_event_thread.daemon = True
            handle_connection_event_thread.start()
            
            with ws_client_lock:
                if client_id in ws_clients:
                    ws_clients[client_id]['threads'].append(handle_connection_event_thread)
            
            print(f"客户端 {client_id} 连接成功: {addr}")
            
        except Exception as e:
            print(f"处理新连接错误: {e}")
            try:
                client_socket.close()
            except:
                pass
    
    def handle_client_messages(client_id):
        """处理客户端消息"""
        while ws_server_running:
            try:
                with ws_client_lock:
                    if client_id not in ws_clients:
                        break
                    client_socket = ws_clients[client_id]['socket']
                
                # 尝试非阻塞接收数据
                try:
                    data = client_socket.recv(4096)
                    if not data:  # 连接已关闭
                        handle_client_disconnection(client_id, "连接关闭")
                        break
                    
                    # 接收到消息，添加到消息队列
                    message_event_queue.put({
                        'type': 'message',
                        'client_id': client_id,
                        'data': data,
                        'timestamp': time.time()
                    })
                    
                    # 启动消息处理线程
                    message_process_thread = threading.Thread(
                        target=handle_message_event, 
                        args=(client_id, data)
                    )
                    message_process_thread.daemon = True
                    message_process_thread.start()
                    
                except BlockingIOError:
                    # 没有数据可读，正常情况
                    time.sleep(0.1)  # 短暂休眠以减少CPU使用
                except ConnectionResetError:
                    # 连接被重置
                    handle_client_disconnection(client_id, "连接被重置")
                    break
                except Exception as e:
                    if ws_server_running:  # 只有在服务器运行时才打印错误
                        print(f"接收客户端消息错误 (ID:{client_id}): {e}")
                    handle_client_disconnection(client_id, f"错误: {e}")
                    break
                
            except Exception as e:
                print(f"消息处理循环错误 (ID:{client_id}): {e}")
                break
    
    def handle_client_disconnection(client_id, reason):
        """处理客户端断开连接"""
        with ws_client_lock:
            if client_id not in ws_clients:
                return
            
            client_info = ws_clients[client_id]
            addr = client_info['addr']
            
            try:
                client_info['socket'].close()
            except:
                pass
            
            # 将断开连接事件添加到队列
            disconnection_event_queue.put({
                'type': 'disconnection',
                'client_id': client_id,
                'addr': addr,
                'reason': reason,
                'timestamp': time.time()
            })
            
            # 启动断开连接事件处理线程
            disconnection_thread = threading.Thread(
                target=handle_disconnection_event, 
                args=(client_id, addr, reason)
            )
            disconnection_thread.daemon = True
            disconnection_thread.start()
            
            # 从客户端列表中移除
            del ws_clients[client_id]
        
        print(f"客户端 {client_id} 断开连接: {addr}, 原因: {reason}")
    
    # 启动事件处理线程
    def start_event_handlers():
        # 启动连接事件处理器
        connection_handler = threading.Thread(target=connection_event_processor)
        connection_handler.daemon = True
        connection_handler.start()
        
        # 启动消息事件处理器
        message_handler = threading.Thread(target=message_event_processor)
        message_handler.daemon = True
        message_handler.start()
        
        # 启动断开连接事件处理器
        disconnection_handler = threading.Thread(target=disconnection_event_processor)
        disconnection_handler.daemon = True
        disconnection_handler.start()
    
    # 事件处理器线程函数
    def connection_event_processor():
        """处理所有连接事件"""
        while ws_server_running:
            try:
                # 非阻塞获取事件
                try:
                    event = connection_event_queue.get(block=False)
                    # 这里可以进行连接事件的集中处理
                    connection_event_queue.task_done()
                except queue.Empty:
                    time.sleep(0.1)  # 队列为空，短暂休眠
            except Exception as e:
                print(f"连接事件处理器错误: {e}")
                time.sleep(0.1)
    
    def message_event_processor():
        """处理所有消息事件"""
        while ws_server_running:
            try:
                # 非阻塞获取事件
                try:
                    event = message_event_queue.get(block=False)
                    # 这里可以进行消息事件的集中处理
                    message_event_queue.task_done()
                except queue.Empty:
                    time.sleep(0.1)  # 队列为空，短暂休眠
            except Exception as e:
                print(f"消息事件处理器错误: {e}")
                time.sleep(0.1)
    
    def disconnection_event_processor():
        """处理所有断开连接事件"""
        while ws_server_running:
            try:
                # 非阻塞获取事件
                try:
                    event = disconnection_event_queue.get(block=False)
                    # 这里可以进行断开连接事件的集中处理
                    disconnection_event_queue.task_done()
                except queue.Empty:
                    time.sleep(0.1)  # 队列为空，短暂休眠
            except Exception as e:
                print(f"断开连接事件处理器错误: {e}")
                time.sleep(0.1)
    
    # 单独的事件处理函数
    def handle_connection_event(client_id, addr):
        """处理连接成功事件"""
        try:
            # 发送欢迎消息
            with ws_client_lock:
                if client_id in ws_clients:
                    client_socket = ws_clients[client_id]['socket']
                    welcome_message = json.dumps({
                        "type": "welcome", 
                        "message": "WebSocket连接已建立", 
                        "client_id": client_id
                    })
                    client_socket.send(welcome_message.encode('utf-8'))
            
            print(f"已向客户端 {client_id} 发送欢迎消息")
        except Exception as e:
            print(f"处理连接事件错误 (ID:{client_id}): {e}")
    
    def handle_message_event(client_id, data):
        """处理接收到消息事件"""
        try:
            # 简单回显接收到的消息
            with ws_client_lock:
                if client_id in ws_clients:
                    client_socket = ws_clients[client_id]['socket']
                    # 在实际应用中，这里可以进行消息解析和业务逻辑处理
                    response_message = json.dumps({
                        "type": "echo", 
                        "message": "收到您的消息", 
                        "client_id": client_id,
                        "data_size": len(data)
                    })
                    client_socket.send(response_message.encode('utf-8'))
            
            print(f"已处理客户端 {client_id} 的消息，大小: {len(data)} 字节")
        except Exception as e:
            print(f"处理消息事件错误 (ID:{client_id}): {e}")
    
    def handle_disconnection_event(client_id, addr, reason):
        """处理断开连接事件"""
        try:
            # 在实际应用中，这里可以进行断开连接后的清理工作
            print(f"已完成客户端 {client_id} 的断开连接处理, 原因: {reason}")
        except Exception as e:
            print(f"处理断开连接事件错误 (ID:{client_id}): {e}")
    
    # 设置运行标志
    ws_server_running = True
    
    # 启动事件处理线程
    start_event_handlers()
    
    # 创建并启动WebSocket服务器主线程
    ws_server_thread = threading.Thread(target=websocket_thread)
    ws_server_thread.daemon = True  # 设置为守护线程，主程序结束时自动结束
    ws_server_thread.start()
    
    # 等待一会儿确保WebSocket服务器启动
    time.sleep(1)
    print("WebSocket服务器已在后台运行")

def stop_websocket_server():
    """
    停止WebSocket服务器
    """
    global ws_server_running, ws_server_thread

    if ws_server_running and ws_server_thread:
        print("正在停止WebSocket服务器...")
        ws_server_running = False

        # 给线程一些时间来完成清理
        if ws_server_thread.is_alive():
            ws_server_thread.join(timeout=3)

        print("WebSocket服务器已停止")

def start_order_monitor():
    """
    启动过期订单监控线程
    """
    global order_monitor_running, order_monitor_thread

    if order_monitor_running:
        print("过期订单监控已在运行中")
        return

    print(f"启动过期订单监控，检查间隔: {order_monitor_interval}秒，过期时间: {order_expiry_hours}小时")

    def order_monitor_thread_func():
        """过期订单监控线程函数"""
        try:
            # 设置Django环境
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ShoppingDjango.settings')

            # 延迟导入Django相关模块，确保Django环境已设置
            import django
            django.setup()

            from django.core.management import call_command
            from django.utils import timezone
            from datetime import timedelta

            print("过期订单监控线程已启动")

            while order_monitor_running:
                try:
                    print(f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始检查过期订单...")

                    # 执行过期订单清理命令
                    call_command('cancel_expired_orders', hours=order_expiry_hours, verbosity=1)

                    print(f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 过期订单检查完成")

                    # 等待下次检查，使用小间隔检查停止标志
                    for _ in range(order_monitor_interval):
                        if not order_monitor_running:
                            break
                        time.sleep(1)

                except Exception as e:
                    print(f"[{timezone.now().strftime('%Y-%m-%d %H:%M:%S')}] 过期订单监控错误: {e}")
                    # 出错后等待较短时间再重试
                    for _ in range(60):  # 等待1分钟
                        if not order_monitor_running:
                            break
                        time.sleep(1)

        except Exception as e:
            print(f"过期订单监控线程初始化失败: {e}")
        finally:
            print("过期订单监控线程已停止")

    # 设置运行标志
    order_monitor_running = True

    # 创建并启动监控线程
    order_monitor_thread = threading.Thread(target=order_monitor_thread_func)
    order_monitor_thread.daemon = True  # 设置为守护线程，主程序结束时自动结束
    order_monitor_thread.start()

    # 等待一会儿确保监控线程启动
    time.sleep(1)
    print("过期订单监控已在后台运行")

def stop_order_monitor():
    """
    停止过期订单监控线程
    """
    global order_monitor_running, order_monitor_thread

    if order_monitor_running and order_monitor_thread:
        print("正在停止过期订单监控...")
        order_monitor_running = False

        # 给线程一些时间来完成清理
        if order_monitor_thread.is_alive():
            order_monitor_thread.join(timeout=5)

        print("过期订单监控已停止")


# ==================== Vue开发服务器管理功能 ====================

def check_vue_environment():
    """检查Vue开发环境"""
    print("🔍 检查Vue开发环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查Node.js
    try:
        node_result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if node_result.returncode == 0:
            print(f"✅ Node.js {node_result.stdout.strip()}")
        else:
            print("❌ Node.js未安装或不可用")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False
    
    # 检查npm
    try:
        npm_result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if npm_result.returncode == 0:
            print(f"✅ npm {npm_result.stdout.strip()}")
        else:
            print("❌ npm不可用")
            print(f"   错误信息: {npm_result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ npm未安装")
        return False
    except Exception as e:
        print(f"❌ npm检查失败: {e}")
        # 尝试备用检查方法
        try:
            import shutil
            npm_path = shutil.which('npm')
            if npm_path:
                print(f"✅ npm 找到路径: {npm_path}")
                # 再次尝试获取版本
                npm_result = subprocess.run([npm_path, '--version'], capture_output=True, text=True)
                if npm_result.returncode == 0:
                    print(f"✅ npm {npm_result.stdout.strip()}")
                else:
                    print("❌ npm版本获取失败")
                    return False
            else:
                print("❌ npm未找到")
                return False
        except Exception as backup_e:
            print(f"❌ npm备用检查也失败: {backup_e}")
            return False
    
    # 检查Django
    try:
        import django
        print(f"✅ Django {django.get_version()}")
    except ImportError:
        print("❌ Django未安装")
        return False
    
    # 检查frontend目录
    if not os.path.exists('frontend'):
        print("❌ frontend目录不存在")
        return False
    
    # 检查manage.py
    if not os.path.exists('manage.py'):
        print("❌ 请在Django项目根目录运行此脚本")
        return False
    
    print("✅ Vue环境检查通过")
    return True


def start_vite_server():
    """启动Vite开发服务器"""
    global vite_server_running, vite_server_thread
    
    if vite_server_running:
        print("Vite开发服务器已在运行中")
        return True
    
    print("🚀 启动Vite开发服务器...")
    
    def vite_thread():
        """Vite服务器线程函数"""
        try:
            # 切换到frontend目录
            original_dir = os.getcwd()
            os.chdir('frontend')
            
            try:
                # 检查node_modules是否存在
                if not os.path.exists('node_modules'):
                    print("📦 安装npm依赖...")
                    npm_install = subprocess.run(['npm', 'install'], capture_output=True, text=True)
                    if npm_install.returncode != 0:
                        print(f"❌ npm install失败: {npm_install.stderr}")
                        return
                
                # 启动Vite开发服务器
                vite_process = subprocess.Popen(
                    ['npm', 'run', 'dev'],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )
                
                # 实时输出Vite日志
                for line in iter(vite_process.stdout.readline, ''):
                    if vite_server_running:
                        print(f"[Vite] {line.rstrip()}")
                    else:
                        break
                        
            finally:
                # 切换回项目根目录
                os.chdir(original_dir)
                
        except Exception as e:
            print(f"❌ Vite服务器启动失败: {e}")
        finally:
            print("Vite服务器已停止")
    
    # 设置运行标志
    vite_server_running = True
    
    # 创建并启动Vite服务器线程
    vite_server_thread = threading.Thread(target=vite_thread)
    vite_server_thread.daemon = True
    vite_server_thread.start()
    
    # 等待一会儿确保Vite服务器启动
    time.sleep(2)
    print("✅ Vite服务器已在后台运行")
    return True


def stop_vite_server():
    """停止Vite开发服务器"""
    global vite_server_running, vite_server_thread
    
    if vite_server_running and vite_server_thread:
        print("正在停止Vite开发服务器...")
        vite_server_running = False
        
        # 给线程一些时间来完成清理
        if vite_server_thread.is_alive():
            vite_server_thread.join(timeout=5)
        
        print("✅ Vite服务器已停止")


def start_django_with_vue():
    """启动Django + Vue混合开发环境"""
    global django_server_process
    
    print("🎯 Django + Vue 混合开发环境")
    print("=" * 50)
    
    # 检查环境
    if not check_vue_environment():
        print("❌ Vue环境检查失败，无法启动混合开发环境")
        return False
    
    try:
        # 启动Django服务器
        print("🚀 启动Django开发服务器...")
        django_server_process = subprocess.Popen(
            [sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 等待Django启动
        time.sleep(2)
        
        # 启动Vite服务器
        if not start_vite_server():
            print("❌ Vite服务器启动失败")
            return False
        
        print("\n🌟 开发服务器已启动:")
        print("   Django: http://localhost:8000")
        print("   Vite:   http://localhost:5173")
        print("   Vue测试页面: http://localhost:8000/admin/backend/vue-test/")
        print("\n按 Ctrl+C 停止服务器")
        
        # 实时输出Django日志
        for line in iter(django_server_process.stdout.readline, ''):
            if django_server_process.poll() is None:
                print(f"[Django] {line.rstrip()}")
            else:
                break
                
    except KeyboardInterrupt:
        print("\n📡 收到中断信号，正在关闭服务器...")
        stop_development_servers()
    except Exception as e:
        print(f"❌ 启动开发环境失败: {e}")
        return False
    
    return True


def stop_development_servers():
    """停止所有开发服务器"""
    global django_server_process
    
    print("🛑 正在停止开发服务器...")
    
    # 停止Vite服务器
    stop_vite_server()
    
    # 停止Django服务器
    if django_server_process:
        try:
            django_server_process.terminate()
            django_server_process.wait(timeout=5)
            print("✅ Django服务器已停止")
        except subprocess.TimeoutExpired:
            django_server_process.kill()
            print("🔥 强制停止Django服务器")
        except Exception as e:
            print(f"❌ 停止Django服务器失败: {e}")
    
    print("✅ 所有开发服务器已停止")


def build_vue_components():
    """构建Vue组件（生产模式）"""
    print("🔨 开始构建Vue组件...")
    
    # 检查环境
    if not os.path.exists('frontend'):
        print("❌ frontend目录不存在")
        return False
    
    if not os.path.exists('frontend/package.json'):
        print("❌ package.json不存在")
        return False
    
    # 切换到frontend目录
    original_dir = os.getcwd()
    os.chdir('frontend')
    
    try:
        # 检查依赖
        if not os.path.exists('node_modules'):
            print("📦 安装npm依赖...")
            result = subprocess.run(['npm', 'install'], capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ npm install失败: {result.stderr}")
                return False
        
        # 构建Vue组件
        print("🚀 构建Vue组件...")
        result = subprocess.run(['npm', 'run', 'build'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Vue组件构建成功")
            if result.stdout.strip():
                print(result.stdout)
            return True
        else:
            print(f"❌ Vue组件构建失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False
    finally:
        os.chdir(original_dir)


def clean_vue_build():
    """清理Vue构建产物"""
    print("🧹 清理Vue构建产物...")
    
    from pathlib import Path
    build_dir = Path('templates/vue')
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print("✅ 清理完成")
    else:
        print("ℹ️  没有需要清理的文件")


def copy_vue_files_for_dev():
    """复制Vue源文件到模板目录（开发模式）"""
    print("📁 复制Vue源文件...")
    
    src_dir = Path('frontend/src')
    dest_dir = Path('templates/vue')
    
    if not src_dir.exists():
        print("❌ Vue源文件目录不存在")
        return False
    
    # 创建目标目录
    dest_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制Vue文件
    vue_files = list(src_dir.rglob('*.vue'))
    
    for vue_file in vue_files:
        # 计算相对路径
        rel_path = vue_file.relative_to(src_dir)
        dest_file = dest_dir / rel_path
        
        # 创建目标目录
        dest_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制文件
        shutil.copy2(vue_file, dest_file)
        print(f"📄 复制: {rel_path}")
    
    print(f"✅ 复制了 {len(vue_files)} 个Vue文件")
    return True

def check_migration_status():
    """
    检查是否有待应用的迁移

    Returns:
        bool: True表示有待应用的迁移，False表示没有
    """
    try:
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ShoppingDjango.settings')

        # 检查数据库连接
        try:
            django.setup()
        except Exception as setup_error:
            print(f"  ✗ Django环境设置失败: {setup_error}")
            return False

        from django.core.management import call_command
        from django.db.migrations.executor import MigrationExecutor
        from django.db import connections, connection
        from django.conf import settings

        # 测试数据库连接
        try:
            connection.ensure_connection()
            print("  ✓ 数据库连接正常")
        except Exception as db_error:
            print(f"  ✗ 数据库连接失败: {db_error}")
            print("    请检查数据库服务是否启动，以及settings.py中的数据库配置")
            return False

        # 获取默认数据库连接
        executor = MigrationExecutor(connection)

        # 获取待应用的迁移
        plan = executor.migration_plan(executor.loader.graph.leaf_nodes())

        if plan:
            print(f"  📋 检测到 {len(plan)} 个待应用的迁移:")
            for migration, backwards in plan:
                status = "⬅️ 回滚" if backwards else "➡️ 应用"
                print(f"    {status} {migration.app_label}.{migration.name}")
            return True
        else:
            print("  ✅ 数据库迁移状态：所有迁移已应用，数据库是最新的")
            return False

    except ImportError as import_error:
        print(f"  ✗ Django导入错误: {import_error}")
        print("    请确保Django已正确安装且在Python路径中")
        return False
    except Exception as e:
        print(f"  ✗ 检查迁移状态时出错: {e}")
        print(f"    错误类型: {type(e).__name__}")
        return False

def check_and_collect_static_files():
    """智能检测并收集静态文件"""
    try:
        from django.conf import settings
        from django.core.management import call_command
        import os

        print("步骤 4/5: 检查静态文件配置...")

        # 检查是否为生产环境
        if not settings.DEBUG:
            static_root = getattr(settings, 'STATIC_ROOT', None)
            if not static_root:
                print("⚠️  生产环境未配置STATIC_ROOT，跳过静态文件收集")
                return True

            print(f"静态文件根目录: {static_root}")

            # 检查静态文件目录是否存在且有内容
            needs_collect = False

            if not os.path.exists(static_root):
                print("静态文件目录不存在，需要收集静态文件")
                needs_collect = True
            else:
                # 检查是否只有Django自带的静态文件
                static_dirs = []
                try:
                    static_dirs = os.listdir(static_root)
                except OSError:
                    pass

                # 如果只有admin和rest_framework，说明缺少项目静态文件
                if set(static_dirs).issubset({'admin', 'rest_framework'}):
                    print("检测到缺少项目静态文件，需要重新收集")
                    needs_collect = True
                elif not static_dirs:
                    print("静态文件目录为空，需要收集静态文件")
                    needs_collect = True
                else:
                    print("✅ 静态文件已存在")

            if needs_collect:
                print("正在收集静态文件...")
                try:
                    # 使用--clear参数清除旧文件，--noinput避免交互，verbosity=2显示详细信息
                    call_command('collectstatic', '--clear', '--noinput', verbosity=2)
                    print("✅ 静态文件收集完成")

                    # 验证收集结果
                    if os.path.exists(static_root):
                        collected_dirs = os.listdir(static_root)
                        print(f"已收集的静态文件目录: {collected_dirs}")

                        # 检查是否包含项目静态文件
                        project_static_indicators = ['css', 'js', 'images']
                        found_project_files = [d for d in collected_dirs if d in project_static_indicators]
                        if found_project_files:
                            print(f"✅ 项目静态文件收集成功: {found_project_files}")
                        else:
                            print("⚠️  警告: 未检测到项目静态文件，请检查STATICFILES_DIRS配置")

                except Exception as e:
                    print(f"⚠️  静态文件收集失败: {e}")
                    print("建议手动执行: python manage.py collectstatic --clear --noinput -v 2")
                    return False
        else:
            print("开发环境，跳过静态文件收集")

        return True

    except Exception as e:
        print(f"检查静态文件时出错: {e}")
        return False

def run_database_migrations():
    """
    执行Django数据库迁移

    Returns:
        bool: True表示迁移成功，False表示迁移失败
    """
    try:
        print("=" * 50)
        print("开始执行数据库迁移...")
        print("=" * 50)

        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ShoppingDjango.settings')

        # 检查Django项目是否存在
        if not os.path.exists('manage.py'):
            print("错误: 未找到manage.py文件，请确保在Django项目根目录下运行")
            return False

        # 第一步：生成新的迁移文件（如果模型有变化）
        print("步骤 1/3: 检查模型变化并生成迁移文件...")
        try:
            result = subprocess.run(
                [sys.executable, 'manage.py', 'makemigrations'],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                if "No changes detected" in result.stdout:
                    print("  ✓ 模型无变化，无需生成新的迁移文件")
                else:
                    print("  ✓ 成功生成新的迁移文件")
                    print(f"    输出: {result.stdout.strip()}")
            else:
                print(f"  ✗ 生成迁移文件失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("  ✗ 生成迁移文件超时")
            return False
        except Exception as e:
            print(f"  ✗ 生成迁移文件时出错: {e}")
            return False

        # 第二步：检查待应用的迁移
        print("步骤 2/3: 检查待应用的迁移...")
        has_pending_migrations = check_migration_status()

        # 第三步：应用迁移
        print("步骤 3/3: 应用数据库迁移...")
        try:
            result = subprocess.run(
                [sys.executable, 'manage.py', 'migrate'],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0:
                print("  ✓ 数据库迁移成功完成")
                if result.stdout.strip():
                    print(f"    详细信息: {result.stdout.strip()}")

                # 最终验证
                print("验证迁移结果...")
                final_check = check_migration_status()
                if not final_check:
                    print("  ✓ 验证通过：所有迁移已成功应用")
                    print("=" * 50)
                    print("数据库迁移完成！")
                    print("=" * 50)
                    return True
                else:
                    print("  ⚠ 警告：仍有未应用的迁移")
                    return False

            else:
                print(f"  ✗ 数据库迁移失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("  ✗ 数据库迁移超时")
            return False
        except Exception as e:
            print(f"  ✗ 应用迁移时出错: {e}")
            return False

    except Exception as e:
        print(f"数据库迁移过程中发生未预期的错误: {e}")
        return False

def get_installed_packages():
    """
    获取当前已安装的Python包列表

    Returns:
        dict: {package_name: version} 格式的字典
    """
    try:
        # 使用pip list命令获取已安装的包
        result = subprocess.run(
            [sys.executable, "-m", "pip", "list", "--format=freeze"],
            capture_output=True,
            text=True,
            timeout=30
        )

        installed_packages = {}
        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if '==' in line:
                    try:
                        package_name, version = line.split('==', 1)
                        installed_packages[package_name.lower()] = version
                    except ValueError:
                        continue

        return installed_packages
    except Exception as e:
        print(f"  ✗ 获取已安装包列表失败: {e}")
        return {}

def parse_requirements_file(requirements_path="requirements.txt"):
    """
    解析requirements.txt文件（支持多种编码格式）

    Args:
        requirements_path (str): requirements文件路径

    Returns:
        list: [(package_name, version), ...] 格式的列表
    """
    requirements = []

    try:
        if not os.path.exists(requirements_path):
            print(f"  ⚠ 未找到 {requirements_path} 文件")
            return requirements

        # 尝试多种编码格式读取文件
        encodings_to_try = ['utf-8', 'utf-16', 'utf-16-le', 'utf-16-be', 'gbk', 'gb2312', 'latin1', 'cp1252']
        lines = []
        successful_encoding = None

        for encoding in encodings_to_try:
            try:
                with open(requirements_path, 'r', encoding=encoding) as f:
                    lines = f.readlines()
                successful_encoding = encoding
                break
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception:
                continue

        if not lines:
            print(f"  ✗ 无法使用任何已知编码读取 {requirements_path} 文件")
            return requirements

        print(f"  ✓ 使用 {successful_encoding} 编码成功读取文件")

        for line_num, line in enumerate(lines, 1):
            line = line.strip()

            # 跳过空行和注释
            if not line or line.startswith('#'):
                continue

            # 移除可能的BOM标记和特殊字符
            line = line.replace('\ufeff', '').replace('\x00', '')

            # 处理包名和版本
            if '==' in line:
                try:
                    package_name, version = line.split('==', 1)
                    package_name = package_name.strip()
                    version = version.strip()

                    # 处理可能的额外条件（如 ; python_version）
                    if ';' in version:
                        version = version.split(';')[0].strip()

                    # 验证包名是否有效（只包含字母、数字、连字符、下划线、点）
                    if package_name and all(c.isalnum() or c in '-_.' for c in package_name):
                        requirements.append((package_name.lower(), version))
                    else:
                        print(f"  ⚠ 第{line_num}行包名格式无效，跳过: {line}")
                except ValueError:
                    print(f"  ⚠ 第{line_num}行格式错误，跳过: {line}")
            else:
                # 尝试处理其他格式（如 >= 或 ~= 等）
                for operator in ['>=', '<=', '>', '<', '~=', '!=']:
                    if operator in line:
                        try:
                            package_name = line.split(operator)[0].strip()
                            if package_name and all(c.isalnum() or c in '-_.' for c in package_name):
                                # 对于非精确版本，我们跳过版本要求，只记录包名
                                requirements.append((package_name.lower(), 'latest'))
                                break
                        except:
                            continue
                else:
                    # 如果没有版本号，可能是简单的包名
                    clean_line = line.strip()
                    if clean_line and all(c.isalnum() or c in '-_.' for c in clean_line):
                        requirements.append((clean_line.lower(), 'latest'))
                    else:
                        print(f"  ⚠ 第{line_num}行不支持的格式，跳过: {line}")

        print(f"  ✓ 成功解析 {len(requirements)} 个依赖包")
        return requirements

    except Exception as e:
        print(f"  ✗ 解析requirements.txt失败: {e}")
        return []

def check_and_install_dependencies():
    """
    检查并自动安装缺失的依赖包

    Returns:
        bool: True表示所有依赖都已安装，False表示安装失败
    """
    try:
        print("=" * 50)
        print("开始检查项目依赖...")
        print("=" * 50)

        # 第一步：解析requirements.txt
        print("步骤 1/4: 解析requirements.txt文件...")
        requirements = parse_requirements_file()

        if not requirements:
            print("  ⚠ 没有找到有效的依赖要求，跳过依赖检查")
            return True

        # 第二步：获取已安装的包
        print("步骤 2/4: 检查已安装的包...")
        installed_packages = get_installed_packages()
        print(f"  ✓ 检测到 {len(installed_packages)} 个已安装的包")

        # 第三步：找出缺失的包
        print("步骤 3/4: 分析缺失的依赖...")
        missing_packages = []
        outdated_packages = []

        for package_name, required_version in requirements:
            if package_name in installed_packages:
                installed_version = installed_packages[package_name]
                if installed_version != required_version:
                    outdated_packages.append((package_name, installed_version, required_version))
            else:
                missing_packages.append((package_name, required_version))

        if not missing_packages and not outdated_packages:
            print("  ✅ 所有依赖都已正确安装")
            print("=" * 50)
            print("依赖检查完成！")
            print("=" * 50)
            return True

        # 显示缺失和版本不匹配的包
        if missing_packages:
            print(f"  📦 发现 {len(missing_packages)} 个缺失的包:")
            for package_name, version in missing_packages[:10]:  # 只显示前10个
                print(f"    - {package_name}=={version}")
            if len(missing_packages) > 10:
                print(f"    ... 还有 {len(missing_packages) - 10} 个包")

        if outdated_packages:
            print(f"  🔄 发现 {len(outdated_packages)} 个版本不匹配的包:")
            for package_name, installed_ver, required_ver in outdated_packages[:5]:  # 只显示前5个
                print(f"    - {package_name}: {installed_ver} -> {required_ver}")
            if len(outdated_packages) > 5:
                print(f"    ... 还有 {len(outdated_packages) - 5} 个包")

        # 第四步：安装缺失的包
        print("步骤 4/4: 安装缺失的依赖...")

        # 使用清华镜像源
        pip_install_cmd = [
            sys.executable, "-m", "pip", "install",
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple",
            "--trusted-host", "pypi.tuna.tsinghua.edu.cn",
            "-r", "requirements.txt"
        ]

        print("  🚀 使用清华镜像源安装依赖...")
        print(f"  执行命令: {' '.join(pip_install_cmd)}")

        try:
            result = subprocess.run(
                pip_install_cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )

            if result.returncode == 0:
                print("  ✅ 依赖安装成功完成")
                if result.stdout.strip():
                    # 只显示关键信息
                    output_lines = result.stdout.strip().split('\n')
                    important_lines = [line for line in output_lines if
                                     'Successfully installed' in line or
                                     'Requirement already satisfied' in line or
                                     'ERROR' in line or
                                     'WARNING' in line]
                    if important_lines:
                        print("  📋 安装摘要:")
                        for line in important_lines[-5:]:  # 只显示最后5行重要信息
                            print(f"    {line}")

                print("=" * 50)
                print("依赖安装完成！")
                print("=" * 50)
                return True
            else:
                print(f"  ✗ 依赖安装失败")
                print(f"  错误信息: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("  ✗ 依赖安装超时（5分钟）")
            return False
        except Exception as e:
            print(f"  ✗ 安装依赖时出错: {e}")
            return False

    except Exception as e:
        print(f"依赖检查过程中发生未预期的错误: {e}")
        return False

def wait_for_django_ready(host="127.0.0.1", port="8000", timeout=60):
    """
    等待Django服务器启动并准备就绪

    Args:
        host (str): Django服务器主机地址
        port (str): Django服务器端口
        timeout (int): 超时时间（秒）

    Returns:
        bool: True表示Django服务器已准备就绪，False表示超时或失败
    """
    print(f"等待Django服务器启动 (http://{host}:{port}/)...")

    # 如果host是0.0.0.0，改为localhost进行健康检查
    check_host = "localhost" if host == "0.0.0.0" else host
    check_url = f"http://{check_host}:{port}/health/"

    start_time = time.time()
    retry_count = 0

    while time.time() - start_time < timeout:
        try:
            # 尝试连接Django服务器
            response = requests.get(check_url, timeout=5)
            if response.status_code in [200, 301, 302, 404]:  # 这些状态码表示服务器已启动
                elapsed_time = time.time() - start_time
                print(f"✅ Django服务器已启动并准备就绪 (耗时: {elapsed_time:.1f}秒)")
                return True
        except requests.exceptions.RequestException:
            # 连接失败，继续重试
            pass

        retry_count += 1
        if retry_count % 5 == 0:  # 每5次重试显示一次进度
            elapsed_time = time.time() - start_time
            print(f"⏳ 等待Django启动中... (已等待: {elapsed_time:.1f}秒)")

        # 使用递增的重试间隔，减少服务器压力
        sleep_time = min(0.5 + (retry_count * 0.1), 2.0)  # 从0.5秒递增到最多2秒
        time.sleep(sleep_time)

    print(f"❌ Django服务器启动超时 (超时时间: {timeout}秒)")
    return False

def check_firewall_status():
    """
    检查系统防火墙状态和类型

    Returns:
        dict: {
            'type': 'firewalld'|'ufw'|'iptables'|'none',
            'active': bool,
            'port_open': bool,
            'needs_config': bool
        }
    """
    result = {
        'type': 'none',
        'active': False,
        'port_open': False,
        'needs_config': False
    }

    try:
        # 检查firewalld
        if shutil.which('firewall-cmd'):
            try:
                status_result = subprocess.run(['systemctl', 'is-active', 'firewalld'],
                                             capture_output=True, text=True, timeout=5)
                if status_result.returncode == 0 and status_result.stdout.strip() == 'active':
                    result['type'] = 'firewalld'
                    result['active'] = True

                    # 检查8000端口是否开放
                    port_result = subprocess.run(['firewall-cmd', '--list-ports'],
                                               capture_output=True, text=True, timeout=5)
                    if port_result.returncode == 0:
                        ports = port_result.stdout.strip()
                        result['port_open'] = '8000/tcp' in ports
                        result['needs_config'] = not result['port_open']

                    return result
            except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                pass

        # 检查ufw
        if shutil.which('ufw'):
            try:
                status_result = subprocess.run(['ufw', 'status'],
                                             capture_output=True, text=True, timeout=5)
                if status_result.returncode == 0:
                    output = status_result.stdout.strip()
                    if 'Status: active' in output:
                        result['type'] = 'ufw'
                        result['active'] = True
                        result['port_open'] = '8000' in output
                        result['needs_config'] = not result['port_open']
                        return result
            except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                pass

        # 检查iptables
        if shutil.which('iptables'):
            try:
                iptables_result = subprocess.run(['iptables', '-L', '-n'],
                                               capture_output=True, text=True, timeout=5)
                if iptables_result.returncode == 0:
                    result['type'] = 'iptables'
                    result['active'] = True
                    # 简单检查是否有8000端口相关规则
                    result['port_open'] = '8000' in iptables_result.stdout
                    result['needs_config'] = not result['port_open']
                    return result
            except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                pass

    except Exception as e:
        print(f"⚠️  防火墙状态检查时出现异常: {e}")

    return result

def configure_firewall_port(port=8000):
    """
    自动配置防火墙开放指定端口

    Args:
        port (int): 要开放的端口号，默认8000

    Returns:
        bool: 配置是否成功
    """
    firewall_status = check_firewall_status()

    if not firewall_status['active']:
        print("ℹ️  系统防火墙未激活，无需配置")
        return True

    if firewall_status['port_open']:
        print(f"✅ 端口 {port} 已在防火墙中开放")
        return True

    if not firewall_status['needs_config']:
        return True

    print(f"🔧 检测到需要在{firewall_status['type']}防火墙中开放端口 {port}")

    # 检查是否有sudo权限
    current_user = getpass.getuser()
    if current_user != 'root':
        print("⚠️  配置防火墙需要管理员权限")

        # 询问用户是否同意自动配置
        try:
            user_input = input(f"是否允许自动配置防火墙开放端口 {port}？(y/n): ").strip().lower()
            if user_input not in ['y', 'yes', '是', '同意']:
                print("❌ 用户取消防火墙自动配置")
                print(f"💡 请手动执行以下命令开放端口 {port}:")
                if firewall_status['type'] == 'firewalld':
                    print(f"   sudo firewall-cmd --permanent --add-port={port}/tcp")
                    print("   sudo firewall-cmd --reload")
                elif firewall_status['type'] == 'ufw':
                    print(f"   sudo ufw allow {port}/tcp")
                elif firewall_status['type'] == 'iptables':
                    print(f"   sudo iptables -A INPUT -p tcp --dport {port} -j ACCEPT")
                return False
        except KeyboardInterrupt:
            print("\n❌ 用户中断防火墙配置")
            return False

    # 执行防火墙配置
    try:
        success = False

        if firewall_status['type'] == 'firewalld':
            print(f"🔧 正在配置firewalld开放端口 {port}...")

            # 添加端口规则
            cmd_prefix = ['sudo'] if current_user != 'root' else []
            add_result = subprocess.run(cmd_prefix + ['firewall-cmd', '--permanent', f'--add-port={port}/tcp'],
                                      capture_output=True, text=True, timeout=10)

            if add_result.returncode == 0:
                # 重新加载防火墙规则
                reload_result = subprocess.run(cmd_prefix + ['firewall-cmd', '--reload'],
                                             capture_output=True, text=True, timeout=10)
                if reload_result.returncode == 0:
                    success = True
                else:
                    print(f"❌ 重新加载防火墙规则失败: {reload_result.stderr}")
            else:
                print(f"❌ 添加防火墙规则失败: {add_result.stderr}")

        elif firewall_status['type'] == 'ufw':
            print(f"🔧 正在配置ufw开放端口 {port}...")
            cmd_prefix = ['sudo'] if current_user != 'root' else []
            result = subprocess.run(cmd_prefix + ['ufw', 'allow', f'{port}/tcp'],
                                  capture_output=True, text=True, timeout=10)
            success = result.returncode == 0
            if not success:
                print(f"❌ ufw配置失败: {result.stderr}")

        elif firewall_status['type'] == 'iptables':
            print(f"🔧 正在配置iptables开放端口 {port}...")
            cmd_prefix = ['sudo'] if current_user != 'root' else []
            result = subprocess.run(cmd_prefix + ['iptables', '-A', 'INPUT', '-p', 'tcp', '--dport', str(port), '-j', 'ACCEPT'],
                                  capture_output=True, text=True, timeout=10)
            success = result.returncode == 0
            if not success:
                print(f"❌ iptables配置失败: {result.stderr}")

        if success:
            print(f"✅ 防火墙端口 {port} 配置成功")

            # 验证配置是否生效
            time.sleep(1)
            verify_status = check_firewall_status()
            if verify_status['port_open']:
                print(f"✅ 验证通过：端口 {port} 已成功开放")
                return True
            else:
                print(f"⚠️  配置完成但验证失败，请手动检查端口 {port} 状态")
                return True  # 仍然返回True，因为命令执行成功了

    except subprocess.TimeoutExpired:
        print("❌ 防火墙配置超时")
    except subprocess.SubprocessError as e:
        print(f"❌ 防火墙配置失败: {e}")
    except Exception as e:
        print(f"❌ 防火墙配置时发生异常: {e}")

    return False

def check_environment_config():
    """
    检查环境配置
    """
    from dotenv import load_dotenv
    load_dotenv()

    django_env = os.getenv('DJANGO_ENV', 'development')
    debug = os.getenv('DEBUG', 'True').lower() == 'true'
    allowed_hosts = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1')

    print("🔧 环境配置检查:")
    print(f"   环境模式: {django_env}")
    print(f"   调试模式: {debug}")
    print(f"   允许主机: {allowed_hosts}")

    # 预生产环境启动拦截
    if django_env == 'preproduction':
        print("=" * 60)
        print("🧪 检测到预生产环境配置")
        print("⚠️  重要提醒: 这是预生产环境，仅用于Git提交前的测试！")
        print("")
        print("如果您要部署到服务器，请先切换到正式生产环境:")
        print("  python deploy.py production [服务器IP] [域名]")
        print("")
        print("继续启动预生产环境服务器？")
        print("  输入 'y' 或 'yes' 继续")
        print("  输入其他任意键退出")
        print("=" * 60)

        try:
            user_input = input("请选择: ").strip().lower()
            if user_input not in ['y', 'yes', '是', '继续']:
                print("已取消启动，请配置正确的环境后重试")
                sys.exit(0)
            else:
                print("✅ 继续启动预生产环境...")
        except KeyboardInterrupt:
            print("\n已取消启动")
            sys.exit(0)

    if django_env == 'production' and debug:
        print("⚠️  警告: 生产环境不应开启DEBUG模式")

    if '*' in allowed_hosts:
        print("⚠️  警告: 允许所有主机访问，请确保这是预期的")

    print("=" * 50)

def start_django_server():
    """
    启动Django服务器（在启动前自动执行数据库迁移）

    Returns:
        tuple: (process, host, port) Django进程对象和连接信息
    """
    # 检查参数
    if len(sys.argv) < 2 or sys.argv[1] != "runserver":
        print("用法: python run.py runserver [端口] [--host=IP]")
        print("如果不指定端口，默认使用8000")
        print("如果不指定host，默认使用0.0.0.0（允许内网访问）")
        sys.exit(1)

    # 在启动Django服务器之前先检查环境配置、依赖，再执行数据库迁移
    print("启动前检查：正在检查环境配置...")
    check_environment_config()

    print("启动前检查：正在检查项目依赖...")
    dependencies_success = check_and_install_dependencies()

    if not dependencies_success:
        print("❌ 依赖检查/安装失败，无法启动Django服务器")
        print("请检查网络连接和requirements.txt文件，解决问题后重新启动")
        stop_websocket_server()
        sys.exit(1)

    print("✅ 依赖检查通过，继续执行数据库迁移...")

    print("启动前检查：正在执行数据库迁移...")
    migration_success = run_database_migrations()

    if not migration_success:
        print("❌ 数据库迁移失败，无法启动Django服务器")
        print("请检查数据库连接和迁移文件，解决问题后重新启动")
        stop_websocket_server()
        sys.exit(1)

    print("✅ 数据库迁移验证通过，继续启动Django服务器...")

    # 检查并收集静态文件
    if not check_and_collect_static_files():
        print("⚠️  静态文件处理失败，但Django服务器将继续启动")
        print("💡 如果静态文件无法加载，请手动执行: python manage.py collectstatic")

    # 构建命令 - 使用当前Python解释器确保环境一致
    cmd = [sys.executable, "manage.py", "runserver"]

    # 解析命令行参数
    port = "8000"
    host = "0.0.0.0"  # 默认监听所有接口，允许内网访问

    for arg in sys.argv[2:]:
        if arg.startswith("--host="):
            host = arg.split("=", 1)[1]
        elif not arg.startswith("--"):
            port = arg

    # 检查端口冲突
    if port == "9000":
        print("❌ 错误: 端口9000已被WebSocket服务器占用，无法启动Django服务器")
        print("💡 建议使用其他端口:")
        print("   python run.py runserver 8000   # 默认端口")
        print("   python run.py runserver 8080   # 常用端口")
        print("   python run.py runserver 3000   # 开发端口")
        print("   python run.py runserver 5000   # 备用端口")
        stop_websocket_server()
        sys.exit(1)

    # 自动配置防火墙（仅在监听所有接口时进行）
    if host == "0.0.0.0":
        print("启动前检查：正在检查防火墙配置...")
        firewall_success = configure_firewall_port(int(port))
        if not firewall_success:
            print("⚠️  防火墙配置失败，但Django服务器将继续启动")
            print("💡 如果外网无法访问，请手动配置防火墙或联系系统管理员")
        print("✅ 防火墙检查完成，继续启动Django服务器...")

    # 添加监听地址和端口
    cmd.append(f"{host}:{port}")

    print(f"正在启动Django服务器...")
    print(f"监听地址: {host}:{port}")
    if host == "0.0.0.0":
        print("✅ 已配置为允许内网访问")
        print("📋 访问方式:")
        print(f"   本地访问: http://localhost:{port}/")
        print(f"   本地访问: http://127.0.0.1:{port}/")
        print(f"   内网访问: http://[您的内网IP]:{port}/")
        print("⚠️  注意: 不要直接访问 http://0.0.0.0:{port}/")
    else:
        print(f"⚠️  仅允许 {host} 访问")
        if host in ["127.0.0.1", "localhost"]:
            print(f"📋 访问地址: http://{host}:{port}/")
    
    # 根据防火墙配置情况提供不同的提示信息
    if host == "0.0.0.0":
        firewall_status = check_firewall_status()
        if firewall_status['active'] and firewall_status['port_open']:
            print(f"✅ Django服务器启动成功，系统防火墙已自动配置")
            print(f"📋 外网访问: 请确保云服务商安全组已开放{port}端口")
        elif firewall_status['active']:
            print(f"⚠️  Django服务器启动成功，但系统防火墙可能需要手动配置")
            print(f"💡 如果外网无法访问，请检查防火墙和云服务商安全组的{port}端口配置")
        else:
            print(f"✅ Django服务器启动成功，请确保云服务商安全组已开放{port}端口")
    else:
        print(f"✅ Django服务器启动成功（仅限{host}访问）")

    try:
        # 使用subprocess.Popen非阻塞启动Django服务器
        process = subprocess.Popen(cmd)

        print("🚀 Django服务器进程已启动，正在等待服务准备就绪...")
        return process, host, port

    except Exception as e:
        print(f"启动Django服务时出错: {e}")
        # 出错时也要停止所有后台服务
        stop_order_monitor()
        stop_websocket_server()
        sys.exit(1)

def main():
    """
    主函数，根据命令行参数执行不同操作
    """
    try:
        # 检查static文件夹下的data.json配置文件
        def ensure_data_config():
            """检查并创建static/data.json配置文件"""
            static_dir = 'static'
            data_file_path = os.path.join(static_dir, 'data.json')

            # 确保static目录存在
            os.makedirs(static_dir, exist_ok=True)

            # 检查data.json文件是否存在
            if not os.path.exists(data_file_path):
                # 创建默认配置
                default_config = {"forestage_templates": "caihong"}
                try:
                    with open(data_file_path, 'w', encoding='utf-8') as f:
                        json.dump(default_config, f, ensure_ascii=False, indent=2)
                    print(f"✅ 已创建默认配置文件: {data_file_path}")
                except Exception as e:
                    print(f"❌ 创建配置文件失败: {e}")

        # 执行配置文件检查
        ensure_data_config()

        # 启动前检测 static/images 下的 SVG 占位图文件
        def ensure_svg_placeholders():
            svg_dir = os.path.join('static', 'images')
            os.makedirs(svg_dir, exist_ok=True)
            svg_files = {
                'category-placeholder.svg': '''<?xml version="1.0" encoding="UTF-8"?>\n<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <title>分类占位图</title>\n    <g id="分类占位图" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <rect id="背景" fill="#E6F7FF" x="0" y="0" width="40" height="40" rx="4"></rect>\n        <g id="文件夹图标" transform="translate(6, 8)" fill="#1890ff">\n            <path d="M0,4 L8,4 C8.55228,4 9,3.55228 9,3 L9,2 C9,1.44772 9.44772,1 10,1 L14,1 C14.5523,1 15,1.44772 15,2 L15,3 C15,3.55228 15.4477,4 16,4 L26,4 C27.1046,4 28,4.89543 28,6 L28,20 C28,21.1046 27.1046,22 26,22 L2,22 C0.89543,22 0,21.1046 0,20 L0,4 Z" id="文件夹主体"></path>\n            <rect x="4" y="10" width="20" height="1" rx="0.5" fill="#E6F7FF"></rect>\n            <rect x="4" y="14" width="16" height="1" rx="0.5" fill="#E6F7FF"></rect>\n            <rect x="4" y="18" width="12" height="1" rx="0.5" fill="#E6F7FF"></rect>\n        </g>\n    </g>\n</svg>''',
                'product-placeholder.svg': '''<?xml version="1.0" encoding="UTF-8"?>\n<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <title>商品占位图</title>\n    <g id="商品占位图" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <rect id="背景" fill="#F5F5F5" x="0" y="0" width="40" height="40" rx="4"></rect>\n        <g id="商品图标" transform="translate(8, 8)" fill="#BFBFBF">\n            <rect x="0" y="0" width="24" height="16" rx="2"></rect>\n            <circle cx="8" cy="8" r="3" fill="#E6F7FF"></circle>\n            <rect x="0" y="18" width="24" height="4" rx="2"></rect>\n        </g>\n    </g>\n</svg>''',
                'order-placeholder.svg': '''<?xml version="1.0" encoding="UTF-8"?>\n<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <title>订单占位图</title>\n    <g id="订单占位图" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <rect id="背景" fill="#FFF7E6" x="0" y="0" width="40" height="40" rx="4"></rect>\n        <g id="订单图标" transform="translate(8, 8)" fill="#FAAD14">\n            <rect x="0" y="0" width="24" height="16" rx="2"></rect>\n            <rect x="0" y="18" width="24" height="4" rx="2"></rect>\n            <rect x="4" y="4" width="16" height="2" rx="1" fill="#FFFBE6"></rect>\n            <rect x="4" y="8" width="10" height="2" rx="1" fill="#FFFBE6"></rect>\n        </g>\n    </g>\n</svg>'''
            }
            for filename, content in svg_files.items():
                file_path = os.path.join(svg_dir, filename)
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
        ensure_svg_placeholders()

        # 检查是否是单独执行迁移命令
        if len(sys.argv) >= 2 and sys.argv[1] == "migrate":
            print("执行数据库迁移...")
            migration_success = run_database_migrations()
            if migration_success:
                print("✅ 数据库迁移完成")
                sys.exit(0)
            else:
                print("❌ 数据库迁移失败")
                sys.exit(1)

        # 检查是否是单独执行依赖检查命令
        elif len(sys.argv) >= 2 and sys.argv[1] == "deps":
            print("检查项目依赖...")
            deps_success = check_and_install_dependencies()
            if deps_success:
                print("✅ 依赖检查完成")
                sys.exit(0)
            else:
                print("❌ 依赖检查失败")
                sys.exit(1)

        # 检查是否是Vue开发模式
        elif len(sys.argv) >= 2 and sys.argv[1] == "dev":
            # 检查环境配置
            check_environment_config()
            
            # 启动Django + Vue混合开发环境
            try:
                # 注册信号处理程序
                def signal_handler(sig, frame):
                    print("\n📡 收到中断信号，正在关闭服务器...")
                    stop_development_servers()
                    stop_websocket_server()
                    stop_order_monitor()
                    sys.exit(0)
                
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)
                
                # 启动WebSocket服务器
                start_websocket_server()
                
                # 启动过期订单监控
                start_order_monitor()
                
                # 启动Django + Vue混合开发环境
                start_django_with_vue()
                
            except KeyboardInterrupt:
                signal_handler(signal.SIGINT, None)
        
        # 检查是否是Vue构建命令
        elif len(sys.argv) >= 2 and sys.argv[1] == "build-vue":
            if len(sys.argv) >= 3:
                build_type = sys.argv[2]
                if build_type == "prod":
                    # 生产构建
                    success = build_vue_components()
                    sys.exit(0 if success else 1)
                elif build_type == "dev":
                    # 开发模式：复制Vue文件
                    success = copy_vue_files_for_dev()
                    sys.exit(0 if success else 1)
                elif build_type == "clean":
                    # 清理构建产物
                    clean_vue_build()
                    sys.exit(0)
                else:
                    print(f"❌ 未知的构建类型: {build_type}")
                    print("用法: python run.py build-vue [prod|dev|clean]")
                    sys.exit(1)
            else:
                # 默认执行开发模式
                print("🎯 Vue组件构建脚本")
                print("=" * 30)
                success = copy_vue_files_for_dev()
                sys.exit(0 if success else 1)

        # 检查是否是runserver命令
        elif len(sys.argv) >= 2 and sys.argv[1] == "runserver":
            # 检查环境配置
            check_environment_config()
            
            # 先启动WebSocket服务器
            start_websocket_server()

            # 启动Django服务器（包含自动迁移）
            django_process, host, port = start_django_server()

            # 等待Django服务器启动完成
            if wait_for_django_ready(host, port):
                print("✅ Django服务器启动成功，现在启动过期订单监控...")
                # Django启动成功后再启动过期订单监控
                start_order_monitor()
                print("🎉 所有服务已启动完成！")
            else:
                print("❌ Django服务器启动失败或超时")
                # 清理已启动的服务
                if django_process:
                    django_process.terminate()
                stop_websocket_server()
                sys.exit(1)

            # 注册信号处理程序，用于处理中断信号
            def signal_handler(sig, frame):
                print("\n接收到中断信号，正在停止所有服务...")
                stop_order_monitor()
                stop_websocket_server()
                if django_process:
                    print("正在停止Django服务器...")
                    django_process.terminate()
                    try:
                        django_process.wait(timeout=5)
                        print("Django服务器已停止")
                    except subprocess.TimeoutExpired:
                        print("强制终止Django服务器...")
                        django_process.kill()
                        django_process.wait()
                print("所有服务已停止")
                sys.exit(0)

            # 注册SIGINT信号处理器（处理Ctrl+C）
            signal.signal(signal.SIGINT, signal_handler)

            # 等待Django进程结束
            try:
                django_process.wait()
            except KeyboardInterrupt:
                signal_handler(signal.SIGINT, None)

            # Django服务器已结束，停止所有后台服务
            stop_order_monitor()
            stop_websocket_server()
            sys.exit(django_process.returncode if django_process else 1)

        else:
            # 显示使用帮助
            print("Django项目启动脚本")
            print("=" * 50)
            print("用法:")
            print("  python run.py runserver [端口] [--host=IP]  - 启动完整服务")
            print("  python run.py dev                          - 启动Django + Vue混合开发环境")
            print("  python run.py build-vue [prod|dev|clean]   - Vue组件构建")
            print("  python run.py migrate                      - 仅执行数据库迁移")
            print("  python run.py deps                         - 仅检查和安装项目依赖")
            print("")
            print("示例:")
            print("  python run.py runserver                         - 默认端口8000，允许内网访问")
            print("  python run.py runserver 9000                    - 使用端口9000，允许内网访问")
            print("  python run.py runserver 8000 --host=127.0.0.1  - 仅本地访问")
            print("  python run.py runserver 8000 --host=localhost   - 仅本地访问")
            print("  python run.py dev                               - 启动Vue混合开发环境")
            print("  python run.py build-vue prod                    - 生产构建Vue组件")
            print("  python run.py build-vue dev                     - 开发模式复制Vue文件")
            print("  python run.py build-vue clean                   - 清理Vue构建产物")
            print("  python run.py migrate                           - 执行数据库迁移")
            print("  python run.py deps                              - 检查和安装依赖")
            print("")
            print("Vue开发环境:")
            print("  python run.py dev 启动后访问:")
            print("  Django: http://localhost:8000")
            print("  Vite:   http://localhost:5173")
            print("  Vue测试页面: http://localhost:8000/admin/backend/vue-test/")
            print("")
            print("监听地址说明:")
            print("  --host=0.0.0.0    - 允许内网访问（默认）")
            print("  --host=127.0.0.1  - 仅本地访问")
            print("  --host=localhost  - 仅本地访问")
            print("")
            print("访问方式:")
            print("  内网访问: http://localhost:端口/ 或 http://127.0.0.1:端口/")
            print("  注意: 不要直接访问 http://0.0.0.0:端口/")
            print("")
            print("环境配置:")
            print("  使用 python deploy.py 命令管理环境配置:")
            print("  python deploy.py development               - 配置开发环境")
            print("  python deploy.py production [IP] [域名]    - 配置生产环境")
            print("  python deploy.py status                    - 查看当前配置")
            print("")
            print("注意:")
            print("  - 默认监听0.0.0.0，允许内网访问")
            print("  - 端口9000被WebSocket服务器占用，请使用其他端口")
            print("  - Vue开发环境需要Node.js和npm支持")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n接收到Ctrl+C，正在停止所有服务...")
        stop_order_monitor()
        stop_websocket_server()
        print("所有服务已停止")
        sys.exit(0)

if __name__ == "__main__":
    main() 